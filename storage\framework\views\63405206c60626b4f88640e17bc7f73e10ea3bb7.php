<?php if($subscription->plan_data): ?>
    <div class="card">
        <div class="card-header">
            <h4 class="card-title"><?php echo app('translator')->get('Plan data'); ?></h4>
            <div class="heading-elements">
                <ul class="list-inline mb-0">
                    <li>
                        <a data-action="collapse">
                            <i data-feather="chevron-down"></i>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
        <div class="card-content collapse show">
            <div class="card-body">
                <ul class="list-group">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span> <?php echo app('translator')->get('Plan id'); ?></span>
                        <span> <?php echo e($subscription->plan_data->id ?? ''); ?> </span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span> <?php echo app('translator')->get('Name'); ?></span>
                        <span> <?php echo e($subscription->plan_data->name ?? ''); ?> </span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span> <?php echo app('translator')->get('Sessions number'); ?></span>
                        <span> <?php echo e($subscription->plan_data->sessions ?? ''); ?> </span>
                    </li>
                    
                        <li class="list-group-item  d-flex justify-content-between align-items-center">
                            <span> <?php echo app('translator')->get('Reservations remainder'); ?>.</span>
                            <span><?php echo e($subscription->remaining_quota['sessions']); ?></span>
                        </li>
                    

                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span> <?php echo app('translator')->get('Type'); ?></span>
                        <span> <?php echo e($subscription->plan_data->type ?? ''); ?> </span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span> <?php echo app('translator')->get('Price'); ?></span>
                        <span> <?php echo e($subscription->plan_data->price ?? 0); ?> </span>
                    </li>
                </ul>
            </div>
        </div>

    </div>
<?php endif; ?>
<?php /**PATH D:\Workstation\Taswk\medhal\Packages\subscriptions\src/../resources/views/themes/vuexy/components/plan-data.blade.php ENDPATH**/ ?>