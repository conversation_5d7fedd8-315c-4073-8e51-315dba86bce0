<?php

namespace Tasawk\Subscriptions\Model;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Tasawk\AdminPanel\Lib\Filters\FilterScope;
use Tasawk\Cms\Model\Place;
use Tasawk\Cms\Model\Plan;
use Tasawk\Employees\Models\Trainer;

class Reservation extends Model {

    use FilterScope;

    protected $table = "reservations";
    protected $guarded = ['id'];
    protected $casts = [
        'date' => 'datetime'
    ];

    protected static function booted() {
        parent::booted();
        static::created(function ($reservation) {
            // Generate unique code with date prefix and unique suffix
            $datePrefix = $reservation->date->format('ymd'); // YYMMDD format
            $uniqueSuffix = str_pad($reservation->id, 3, '0', STR_PAD_LEFT); // 3-digit ID
            $microtime = substr(microtime(true) * 1000, -3); // Last 3 digits of microtime

            $reservation->code = $datePrefix . $uniqueSuffix . $microtime;
            $reservation->save();
        });
    }


    public function subscribers(): HasMany {
        return $this->hasMany(ReservationSubscriber::class);
    }

    public function joinFreeSubscribe($user) {
        return $this->subscribers()->create([
            'subscriber_id' => $user->id,
            'status' => 'pending',
            'type' => ReservationSubscriber::FREE,
            'data'=>[
                'name'=>request()->name
            ]
        ]);
    }
    public function joinEvaluationSubscribe($user, $subscription) {

        return $this->subscribers()->create([
            'subscriber_id' => $user->id,
            'subscription_id' => $subscription->id ?? '',
            'status' => 'pending',
            'type' => ReservationSubscriber::Evaluation
        ]);
    }

    public function joinSubscriber($user, $subscription) {
        $subscription->decrementSessionsQuota();

        return $this->subscribers()->create([
            'subscriber_id' => $user->id,
            'subscription_id' => $subscription->id,
            'status' => 'pending',
            'type' => ReservationSubscriber::SUBSCRIPTION
        ]);
    }

    public function trainer() {
        return $this->belongsTo(Trainer::class);
    }
}
