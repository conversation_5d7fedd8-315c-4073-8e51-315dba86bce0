<nav class="header-navbar navbar navbar-expand-lg align-items-center floating-nav navbar-light navbar-shadow ">
    <div class="navbar-container d-flex content">
        <div class="bookmark-wrapper d-flex align-items-center">
            <ul class="nav navbar-nav d-xl-none">
                <li class="nav-item"><a class="nav-link menu-toggle" href="javascript:void(0);"><i class="ficon"
                            data-feather="menu"></i></a></li>
            </ul>
            <ul class="nav navbar-nav bookmark-icons navbar-actions align-items-center">
                <li class="nav-item d-none d-lg-block">
                    <span class="!avatar">
                        <img class="round" src="<?php echo e(display_logo_avatar()); ?>"
                            onerror="this.src = 'app-assets/images/default/logo.svg'"
                            alt="<?php echo e(settings()->group('default')->get('company_name', config('app.name'))); ?>"
                            height="40" width="40" data-style="max-width: 25px;">
                    </span>
                </li>
                
                <?php
                $navbar_actions_views = Eventy::filter('navbar.actions.views', []);
                ?>
                
            </ul>
        </div>
        <ul class="nav navbar-nav align-items-center ml-auto">
            
            
            
            
            <li class="nav-item d-none d-lg-block">
                <?php $convertLang = app()->getLocale() == 'en' ? 'ar' : 'en'; ?>
                <a style="font-family: system-ui" hreflang="<?php echo e($convertLang); ?>"
                    href="<?php echo e(LaravelLocalization::getLocalizedURL($convertLang, null, [], true)); ?>"
                    data-language="<?php echo e($convertLang); ?>" class="nav-link a_lang">
                    <?php echo e(\App::getLocale() == 'en' ? 'العربية' : 'English'); ?>

                </a>
            </li>
            <?php app('eventy')->action('navbar.actions.right'); ?>
            
            <?php
            /*
                                                                                                            ?>
            ?>
            ?>
            ?>
            ?>
            ?>
            ?>
            ?>
            ?>
            <li class="nav-item d-none d-lg-block">
                <a class="nav-link nav-link-style"
                    data-user-setting='{"dark-mode":"{{ setting_user()->get('dark-mode', false) ? 'false' : 'true' }}"}'>
                    <i class="icon-sun3"></i>
                </a>
            </li>
            <?php
            */
            ?>
            <li class="nav-item dropdown dropdown-user">
                <a class="nav-link dropdown-toggle dropdown-user-link" id="dropdown-user" href="javascript:void(0);"
                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <div class="user-nav d-sm-flex d-none"><span
                            class="user-name font-weight-bolder"><?php echo e(auth()->user()->name); ?></span>
                        <span class="user-status"><?php echo e(optional(auth()->user()->roles->first())->name); ?></span>
                    </div>
                    <span class="avatar">
                        <img class="round" src="<?php echo e(display_user_avatar()); ?>"
                            onerror="this.src = 'app-assets/images/default/avatar.png'" height="40" width="40">
                    </span>
                </a>
                <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdown-user">
                    <?php app('eventy')->action('navbar.user.actions.before'); ?>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check("manage_settings.general")): ?>
                        <a class="dropdown-item" href="<?php echo e(url('admin/settings/my-profile')); ?>">
                            <i class="mr-50" data-feather="settings"></i> <?php echo app('translator')->get('General settings'); ?>
                        </a>
                    <?php endif; ?>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check("manage_settings.profile")): ?>
                        <a class="dropdown-item" href="<?php echo e(url('my_profile')); ?>">
                            <i class="mr-50" data-feather="user"></i> <?php echo app('translator')->get('Profile'); ?>
                        </a>
                    <?php endif; ?>
                    <div class="dropdown-divider"></div>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('manage_settings.general')): ?>
                        <a class="dropdown-item" href="<?php echo e(route('app_settings.index')); ?>"><i class="mr-50"
                                data-feather="settings"></i> <?php echo app('translator')->get('Settings'); ?></a>
                    <?php endif; ?>
                    <a class="dropdown-item" href="<?php echo e(route('logout')); ?>"><i class="mr-50" data-feather="power"></i>
                        <?php echo app('translator')->get('Logout'); ?></a>
                    <?php app('eventy')->action('navbar.user.actions.after'); ?>
                </div>
            </li>
        </ul>
    </div>
</nav>
<?php /**PATH D:\Workstation\Taswk\medhal\Packages\admin-theme-vuexy\src/views/parts/nav.blade.php ENDPATH**/ ?>