<?php

namespace Tasawk\Subscriptions\Model;

use App\Models\User;
use Tasawk\Cms\Model\Plan;
use AM\Rates\Contracts\Rateable;
use Spatie\MediaLibrary\HasMedia;
use Darryldecode\Cart\CartCondition;
use Tasawk\Employees\Models\Trainer;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\InteractsWithMedia;
use Tasawk\Ecommerce\Lib\Cart\ArrayStorage;
use Tasawk\Employees\Models\Specialization;
use Tasawk\AdminPanel\Lib\Filters\FilterScope;
use Tasawk\Ecommerce\Lib\Extend\Cart as CoreCart;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Subscription extends Model implements HasMedia {
    use FilterScope, InteractsWithMedia;
    use Rateable;

    protected $table = "subscriptions";
    protected $guarded = ['id'];
    protected $casts = [
        'remaining_quota' => 'json',
        'payment_data' => 'json',
        'cart_data' => 'json',
        'plan_data' => 'object',
        'data' => 'object',
        'expires_at' => 'datetime',
    ];

    protected static function booted() {
        parent::booted(); // TODO: Change the autogenerated stub
        //        static::addGlobalScope("paid", fn($builder) => $builder->paid());
    }

    function itemsLine() {
        return $this->hasMany(ItemsLine::class);
    }

    function conditions() {
        return $this->hasMany(Conditions::class);
    }

    public function scopePaid($builder) {
        return $builder->where('payment_status', 'paid')->orWhere('payment_data', '"in_center"');
    }

    public function getRemainingSessionCountAttribute() {
        if ($this->type == 'online') {
            return $this->plan?->sessions_number - $this->reservations->where('status', "!=", 'canceled')->count();
        }
        return $this->remaining_quota['sessions'];
    }

//    public function getSessionsCountAttribute() {
//         $this->plan_data->sessions
//}
    public function getStatusAttribute() {
        if ($this->report()->exists()) {
            return "Reported";
        }
        if (!$this->active) {
            return "Canceled";
        }
        if ($this->payment_data == 'in_center' && $this->type == 'online' && $this->payment_status != 'paid') {
            return "Waiting management confirmation";
        }
//        if ($this->type == 'in_center' && $this->remaining_sessions == $this->reservations()->where('status', 'completed')->count()) {
//            return "Completed";
//        }
          if ($this->timeIsGone() || $this->reservationsCompleted()) {
            return "Completed";
        }
        if (
            $this->type == 'in_center' &&
            !$this->reservationsCompleted() && $this->remaining_quota['sessions'] > 0
        ) {
            return "Running";
        }
//        if (!$this->evaluationReservationCompleted() && !$this->timeIsGone() && $this->type == 'online') {
//            return "Pending Evaluation Reservation";
//        }

        if ($this->expires_at->subDays($this->plan->expiration_duration) <= now() && !$this->timeIsGone() && $this->hasSessionLeft()) {
            return 'Additional validity period';
        }
        if (
//            $this->evaluationReservationCompleted()
//            &&
            !$this->reservationsCompleted() && !$this->timeIsGone()
            && $this->type == 'online'
        ) {
            return "Running";
        }

      
        if (
            $this->type == 'in_center' && $this->remaining_quota['sessions'] == 0 && $this->reservationsCompleted()
        ) {
            return "Completed";
        }
        if (
            $this->type == 'in_center' && $this->remaining_quota['sessions'] == 0
        ) {
            return "Running";
        }
    }

    public function scopeGetCurrentMonthSubscription($builder) {
        return $builder
            ->where(\DB::raw("DATE_FORMAT(created_at,'%Y-%m')"), now()->format('Y-m'))
            ->where('expires_at', ">", now())
            ->where('active', 1)
            ->first();
    }

    public function trainer(): BelongsTo {
        return $this->belongsTo(Trainer::class);
    }

    public function decrementSessionsQuota() {
        $quote = array_merge($this->remaining_quota, ['sessions' => $this->remaining_quota['sessions'] - 1]);
        return $this->update(['remaining_quota' => $quote]);
    }

    public function reservationsCompleted(): bool {
        return $this->reservations()->where('status', 'completed')->count()  == $this->plan_data->sessions;
    }

    public function incrementSessionsQuota() {
        $quote = array_merge($this->remaining_quota, ['sessions' => $this->remaining_quota['sessions'] + 1]);
        return $this->update(['remaining_quota' => $quote]);
    }

    public function cancel(): bool {
        return $this->update(['active' => 0]);
    }

    public function user() {
        return $this->belongsTo(User::class, "user_id");
    }

    public function paymentOwner() {
        return $this->belongsTo(User::class, "payment_owner_id");
    }

    public function plan() {
        return $this->belongsTo(Plan::class, "plan_id");
    }

    public function reservations() {
        return $this->hasMany(ReservationSubscriber::class);
    }

    public function reserveEvaluationReservationBefore() {
        return $this->reservations()->where('type', ReservationSubscriber::Evaluation)->where('status', "!=", 'canceled')->exists();
    }

    public function evaluationReservation() {
        return $this->reservations()->where('type', ReservationSubscriber::Evaluation)->where('status', "!=", 'canceled')->first();
    }

    public function subscribedReservations() {
        return $this->reservations()->where('type', ReservationSubscriber::SUBSCRIPTION)->where('status', '!=', 'canceled')->latest();
    }

    public function evaluationReservationCompleted() {
        if (!$this->reserveEvaluationReservationBefore()) {
            return false;
        }
        return $this->reservations()->where('type', ReservationSubscriber::Evaluation)->where('status', 'completed')->exists();
    }

    public function hasSessionLeft(): bool {
        return $this->remaining_quota['sessions'] > 0;
    }

    public function timeIsGone() {
        return $this->expires_at < now();
    }

    public function customerReport() {
        return $this->hasOne(Report::class);
    }

    public function classification(): BelongsTo {
        return $this->belongsTo(Specialization::class, 'classification_id');
    }

    public function report() {
        return $this->hasOne(Report::class);
    }

    public function nextReservation() {

        $obj = new \ArPHP\I18N\Arabic();
        $obj->setNumberFeminine(2);
        $obj->setNumberFormat(2);
        $obj->setNumberOrder(2);
        if ($this->subscribedReservations()->count() > $this->plan_data->sessions) {
            return [];
        }

        if (!$this->subscribedReservations()->count()) {

            return [
                "id" => null,
                'name' => __("Reservation :NO", ['NO' => $obj->int2str(1)]),
                'date' => null,
                'duration' => 45,
                'classification' => $this->classification?->title,
                'code' => null,
                'plan_duration' => $this->plan_data?->duration,
                'trainer' => null,
                'can_be_canceled' => false
            ];
        }

        if ($this->subscribedReservations()->count() && $this->subscribedReservations()->first()->status !== 'completed') {
            $reservation = $this->subscribedReservations()->first();
            return [
                "id" => $reservation->id,
                'name' => __("Reservation :NO", ['NO' => $obj->int2str($this->subscribedReservations()->count())]),
                'date' => $reservation?->reservation->date->format("Y-m-d h:i a"),
                'duration' => 45,
                'classification' => $this->classification?->title,
                'code' => $reservation?->reservation?->code,
                'plan_duration' => $this->plan_data?->duration,
                'trainer' => $reservation?->reservation?->trainer?->name,
                'can_be_canceled' => $reservation->canBeCanceled()
            ];
        }
        if ($this->subscribedReservations()->where('status', 'completed')->count() == $this->plan_data->sessions) {
            return [];
        }
        if ($this->subscribedReservations()->count() && $this->subscribedReservations()->first()->status == 'completed') {
            return [
                "id" => null,
                'name' => __("Reservation :NO", ['NO' => $obj->int2str($this->subscribedReservations()->count() + 1)]),
                'date' => null,
                'duration' => 45,
                'classification' => $this->classification?->title,
                'code' => null,
                'plan_duration' => $this->plan_data?->duration,
                'trainer' => null,
                'can_be_canceled' => false
            ];
        }

        return [];
    }

    public function getAsCartAttribute() {
        $eventsClass = config('shopping_cart.events');
        $events = $eventsClass ? new $eventsClass() : app('events');
        $session_key = md5($this->cart_id . \Str::random());
        $instanceName = $session_key . 'back_end_order_cart';
        $cart = new CoreCart(
            new ArrayStorage,
            $events,
            $instanceName,
            $session_key,
            config('shopping_cart')
        );


        //        collect([1, 2, 3])->transform(fn($n) => $n * $n)->each(fn($i) => dump($i));
        //        dd('');

        $this->itemsLine->transform(function ($item) {
            $conditions = collect($item->conditions)->map(fn($cond) => new CartCondition($cond))->toArray();
            $item['quantity'] = $item->quantity > 0 ? $item->quantity : 1;
            $item['associatedModel'] = $item->model;
            $item['new_conditions'] = $conditions;
            return $item;
        })->each(function ($item) use ($cart) {
            $d = $item->toArray();
            $d['conditions'] = $d['new_conditions'];
            return $cart->add($d);
        });
        $this->conditions->each(fn($condition) => $cart->condition(new CartCondition($condition->toArray())));
        return $cart;
    }

    public function canChangeSubscriptionTrainerTo($trainer): bool {
        if ($this->trainer_id == $trainer->id) {
            return true;
        }
        return $this->plan->trainers()->where('id', $this->trainer_id)->first()?->pivot?->price <= 0;
    }
}
