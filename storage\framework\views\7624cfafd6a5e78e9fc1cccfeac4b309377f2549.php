<?php

use Tasawk\AdminPanel\Lib\Breadcrumb;use TorMorten\Eventy\Facades\Eventy;

$breadcrumb = new Breadcrumb();
$currentRoute = request()->route()->getName();
$page_breadcrumbs = Eventy::filter('page.breadcrumbs.' . $currentRoute, Eventy::filter('page.breadcrumbs', []));
$breadcrumb->bulk($page_breadcrumbs)->setUlClass(['breadcrumb', 'asdasd']);
if ($breadcrumb->isEmpty()) {
    return;
}
?>
<style>

</style>

<section id="default-breadcrumb">
    <div class="row">
        <div class="col-sm-12">
            <div class="card">

                <div class="card-body d-flex justify-content-between align-items-center">
                    <div class=" content-header d-flex">
                        <h2 class="content-header-title float-left mb-0 d-md-block d-sm-none d-none"><?php echo $__env->yieldContent("title"); ?></h2>
                        <div class="breadcrumb-wrapper">
                            <nav aria-label="breadcrumb ">
                                <ol class="breadcrumb">
                                    <?php $__currentLoopData = $breadcrumb->getList(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                                        <li class="breadcrumb-item <?php if($loop->last): ?> active text-dark <?php endif; ?>">
                                            <?php if($loop->last): ?>
                                                <?php echo e($item->getText()); ?>

                                            <?php else: ?>
                                                <?php if(!empty($item->getLink())): ?>
                                                    <a href="<?php echo e($item->getLink()); ?>"><?php echo e($item->getText()); ?></a>
                                                <?php else: ?>
                                                    <span class="text-primary"><?php echo e($item->getText()); ?></span>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ol>
                            </nav>
                        </div>
                    </div>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <?php
                            $breadcrumb_actions = Eventy::filter('breadcrumb.actions.' . $currentRoute, Eventy::filter('breadcrumb.actions', []));
                            $breadcrumb_actions += Eventy::filter('breadcrumb.actions.' . request()->route()->getName(), []);
                            ?>
                            <?php $__currentLoopData = $breadcrumb_actions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $action): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>

                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check(Arr::get($action,"permission"))): ?>
                                    <li class="mr-1">
                                        <?php if(isset($action['is-modal-button']) && $action['is-modal-button']): ?>
                                            <button type="button"
                                                    data-form-view="<?php echo e($action['modal-content-view']); ?>"
                                                    class="btn btn-primary waves-effect"
                                                    data-toggle="modal"
                                                    data-target="#add-resource-modal">
                                                <?php echo e($action['title']); ?>

                                            </button>
                                        <?php else: ?>
                                            <a href="<?php echo e($action['url']); ?>"
                                               class="btn btn-sm d-inline-block <?php echo e(isset($action['classes'])?$action['classes']:"btn-dark"); ?> ">
                                                <span><?php echo e($action['text']); ?></span>
                                            </a>
                                        <?php endif; ?>
                                    </li>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</section>

<?php /**PATH D:\Workstation\Taswk\medhal\Packages\admin-theme-vuexy\src/views/parts/breadcrumb.blade.php ENDPATH**/ ?>