<?php return;?>
<?php $componentsData = \TorMorten\Eventy\Facades\Eventy::filter('admin.search.data', []); ?>

<script>
    (function () {
        var SearchData = <?php echo json_encode($componentsData, 15, 512) ?>;
        const options = {
            keys: [
                "title",
                "description",
                "url"
            ]
        };

        let fuseCB = function () {
            const fuse = new Fuse(SearchData, options);
            const pattern = $('#recipient-name').val();
            $('#searchResault').html(' ');
            for (let i = 0; i < fuse.search(pattern).length; i++) {
                $('#searchResault').append('<li class="auto-suggestion"><a class="d-flex align-items-center justify-content-between w-100" href="' + fuse.search(pattern)[i].item.url + '"><div class="d-flex"><div class="mr-75"></div><div class="search-data"><p class="search-data-title mb-0">' + fuse.search(pattern)[i].item.title + '</p></a></li>');
                // $('#searchResault').append('<li style="float: none"><a href="'+fuse.search(pattern)[i].item.url+'" class="sidebar-control sidebar-main-toggle hidden-xs">'+fuse.search(pattern)[i].item.title+'</a></li>');
            }
        }

        $(document).on('keypress', '#recipient-name', fuseCB);
        $(document).on('click', '.search-input-icon', fuseCB);
    })();
</script>
<?php /**PATH D:\Workstation\Taswk\medhal\Packages\utilities\src/../resources/views/themes/vuexy/search/common/scripts.blade.php ENDPATH**/ ?>