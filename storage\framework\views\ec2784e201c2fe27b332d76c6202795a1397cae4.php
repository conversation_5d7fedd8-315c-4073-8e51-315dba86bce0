<?php
use Tasawk\AdminPanel\Lib\Filters\FormBuilder;
?>
<?php $attributes = $attributes->exceptProps([
'id' => '',
'filters' => [],
'orderBy' => [],
"filter_unique_panel_title"=>Str::of($id)->finish("-card")->slug()->lower()
]); ?>
<?php foreach (array_filter(([
'id' => '',
'filters' => [],
'orderBy' => [],
"filter_unique_panel_title"=>Str::of($id)->finish("-card")->slug()->lower()
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>
<?php if(!empty($filters)): ?>
    <div class="index-filter card card-default <?php echo e(get_user_panel_collapse_state_class($id)); ?>"
         data-panel-id="<?php echo e($id); ?>">
        <div class="card-header">
            <p class="card-title d-flex align-items-center">
                <i class="icon-search4 "></i>
                <span class="ml-1"><?php echo app('translator')->get("Filter"); ?></span>
            </p>
            <div class="heading-elements">
                <ul class="list-inline mb-0">
                    <li>
                        <a data-action="collapse"
                           data-user-setting='{"<?php echo e($filter_unique_panel_title); ?>":"<?php echo e(setting_user()->get("$filter_unique_panel_title") == "true" ? 'false' : 'true'); ?>"}'>
                            <?php if(setting_user()->get("$filter_unique_panel_title") == "true" ): ?>
                                <i data-feather="chevron-down"></i>
                            <?php else: ?>
                                <i data-feather="chevron-up"></i>
                            <?php endif; ?>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
        <div class="card-content collapse <?php echo e(setting_user()->get("$filter_unique_panel_title") =="true"?"show":""); ?>">

            <div class="card-body table-responsive">
                <form method="get" id="page-filter" class="form-horizontal">
                    <div class="row">
                        <?php echo FormBuilder::build($filters) ?>
                        <input type="hidden" name="filtered" value="1">
                    </div>
                    <?php if(!empty($orderBy)): ?>
                        <div class="clearfix"></div>
                        <hr/>
                        <div class="row">
                            <?php echo FormBuilder::build($orderBy) ?>
                        </div>
                    <?php endif; ?>
                </form>
            </div>
            <div class="card-footer mb-3">
                <a class="heading-elements-toggle"></a>
                <div class="heading-elements float-right">
                    <button type="submit"
                            form="page-filter"
                            class="btn btn-primary heading-btn"><?php echo app('translator')->get('Search'); ?>
                    </button>
                    <a type="button"
                       href="<?php echo e(request()->url()); ?>"
                       form="page-filter"
                       class="btn btn-dark heading-btn">
                        <?php echo app('translator')->get('Reset'); ?>
                    </a>
                </div>
            </div>
        </div>

    </div>
<?php endif; ?><?php /**PATH D:\Workstation\Taswk\medhal\Packages\admin-theme-vuexy\src/views/components/filter.blade.php ENDPATH**/ ?>