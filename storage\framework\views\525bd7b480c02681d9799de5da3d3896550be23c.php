<?php
    $result = '';
    $data = \TorMorten\Eventy\Facades\Eventy::filter('admin.help.route.data', []);
    $currentRouteName = \Route::currentRouteName();
    if (isset($data[$currentRouteName])) {
        $result = $data[$currentRouteName];
    }
?>

<div class="modal fade text-left" id="help" tabindex="-1" role="dialog" aria-labelledby="myModalLabel1" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="myModalLabel1"><?php echo app('translator')->get("Help"); ?></h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <?php echo $result; ?>

            </div>
        </div>
    </div>
</div><?php /**PATH D:\Workstation\Taswk\medhal\Packages\utilities\src/../resources/views/themes/vuexy/help/modal.blade.php ENDPATH**/ ?>