<footer class="footer footer-static footer-light">
    <p class="clearfix mb-0">
        <span class="float-md-left d-block d-md-inline-block mt-25"> &copy; <?php echo e(date('Y')); ?>

            <a class="ml-25" href="<?php echo e(config('app.url')); ?>" target="_blank"><?php echo e(config('app.name')); ?></a>
            <span class="d-none d-sm-inline-block">, <?php echo app('translator')->get('All rights Reserved'); ?></span>
        </span>
    </p>
</footer>
<?php echo $__env->make('admin::parts.footer.session-ends-overlay', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<button class="btn btn-primary btn-icon scroll-top" type="button"
    style="position: fixed;display: none;bottom: 0; <?php if(!Utilities::isRtl()): ?> right: 0; <?php else: ?> left:0 <?php endif; ?>"><i
        data-feather="arrow-up"></i></button>
<?php echo $__env->yieldPushContent('modals'); ?>
<script src="<?php echo e(mix('assets/tenant/site/js/tenant.js')); ?>"></script>














<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.3/js/i18n/<?php echo e(app()->getLocale()); ?>.js"></script>
<?php $__currentLoopData = Eventy::filter('admin.document.js', []); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $url => $attrs): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <?php echo e(html()->element('script')->attribute('src', $url)->attributes($attrs)); ?>

<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<script>
    ;

    function initFeather() {
        if (feather) {
            feather.replace({
                width: 14,
                height: 14
            });
        }
    }

    setTimeout(() => {
        $('body .file-input').dropify({
            messages: {
                default: "<?php echo app('translator')->get('Drag and drop a file here or click'); ?>",
                replace: "<?php echo app('translator')->get('Drag and drop or click to replace'); ?>",
                remove: "<?php echo app('translator')->get('Remove'); ?>",
                error: "<?php echo app('translator')->get('Ooops, something wrong happended.'); ?>"
            },
            error: {
                fileSize: "<?php echo app('translator')->get('The file size is too big .'); ?>",
                minWidth: "<?php echo app('translator')->get('The image width is too small .'); ?>",
                maxWidth: "<?php echo app('translator')->get('The image width is too big .'); ?>",
                minHeight: "<?php echo app('translator')->get('The image height is too small .'); ?>",
                maxHeight: "<?php echo app('translator')->get('The image height is too big .'); ?>",
                imageFormat: "<?php echo app('translator')->get('The image format is not allowed .'); ?>"
            }
        });
    }, 500);
    <?php if(session()->has('alert') && ($alert = session()->get('alert'))): ?>
        notify(...Object.values(<?php echo json_encode($alert, 15, 512) ?>));
    <?php endif; ?>
    function notify(title, message, type = 'success') {
        var isRtl = $('html').attr('data-textdirection') === 'rtl';
        return toastr[type](message, title, {
            closeButton: true,
            tapToDismiss: true,
            preventDuplicates: true,
            progressBar: true,
            closeDuration: 300,
            rtl: isRtl
        });
    }

    $(window).on('load', function() {
        initFeather()
    })
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': _token
        }
    });
    setTimeout(function() {
        $("select.select-search").select2({
            allowClear: true
        });
        $(".select-multiple-tags").select2();
    });
    $(document).ready(function() {
        $('.navigation-main>li').each(function() {
            var _this = this;
            if (!$(_this).hasClass('has-sub')) {
                $(_this).removeClass('active nav-item');
                var link = $(_this).find('a').attr('href');
                if (link == window.location.href) {
                    $(_this).addClass('active nav-item');
                }
            } else {
                $(_this).find('ul').find('li').find('a').each(function() {
                    $(this).parent().removeClass('active nav-item');
                    var link = $(this).attr('href');
                    if (link == window.location.href) {
                        $(this).parent().addClass('active nav-item');
                    }
                });
            }
        });
    });
    var App;;
    (function(w, d, $) {
        w['App'] = {
            helpers: {
                
                
                
                
                
            },
            view: {
                el: null,
                elData: [],
                setEl: function(selector) {
                    this.el = $(selector);
                    this.el.data
                    return this;
                },
                hideWithFadeOut: function() {
                    let speed = 'slow';
                    if (this.el.length) {
                        speed = this.el.data('fade-speed') ? this.el.data('fade-speed') : speed;
                        this.el.fadeOut(speed);
                    }
                }
            }
        };
    })(window, document, jQuery);

    function noticeWithClickToClose(type, title, text = '') {
        let stack_top_left = {
            "dir1": "down",
            "dir2": "right",
            "push": "top"
        };
        let notice = new PNotify({
            title: title,
            text: text,
            buttons: {
                closer: false,
                sticker: false
            },
            icon: 'icon-checkmark3',
            type: type,
            addclass: 'stack-top-right',
            stack: stack_top_left
        });
        notice.get().click(function() {
            notice.remove();
        });
    }

    function blockItem(selector) {
        return $(selector).block({
            message: '<i class="icon-spinner4 spinner"></i>',
            overlayCSS: {
                backgroundColor: '#fff',
                opacity: 0.8,
                cursor: 'wait'
            },
            css: {
                border: 0,
                padding: 0,
                backgroundColor: 'transparent'
            }
        });
    }

    // todo remove Jquery ajax and use js fetch
    $('.panel [data-action=collapse]').click(function(e) {
        let _el = $(this);
        let panel = _el.parents('.panel');
        setTimeout(function() {
            let isCollapsed = panel.hasClass('panel-collapsed');
            $.ajax({
                'url': '<?php echo e(route('update.panels.collapse.state')); ?>',
                'data': {
                    '_token': _token,
                    'panel': panel.data('panel-id'),
                    'state': isCollapsed,
                },
                'method': 'POST'
            });
        });
    });

    function convertInputDotedNameToHtmlArray(name) {
        //cities_price[5][sale_price]
        let res = name.split(".");
        // console.log(res)
        // return
        let _name = '';
        for (let i = 0; i < res.length; i++) {
            if (i === 0) {
                _name += res[i];
                continue;
            }
            // if (!isNaN(parseInt(res[i]))) {
            //     _name += '[]'
            //     continue;
            // }
            _name += '[' + res[i] + ']'
        }
        console.log(_name)
        return _name;
    }

    $('[data-has-ajax]').click(function(e) {
        e.preventDefault();
        let el = $(this);
        let form = $(el.data('ajax-form'));
        let ui_block = el.data('ajax-ui-block')
        let block = blockItem(ui_block);
        let message = form.find("input[name=_method]").val() === "PUT" ? "👋 <?php echo app('translator')->get('Data updated successfully'); ?>" :
            "👋 <?php echo app('translator')->get('Data created successfully'); ?>";
        message = el.data('custom-success-message') || message;
        let request = fetch(form.attr('action'), {
            headers: {
                'X-CSRF-TOKEN': _token,
                "X-Requested-With": "XMLHttpRequest",
            },
            method: 'POST',
            body: new FormData(form[0])
        })
        request.then(function(response) {
            block.unblock();
            block.find('.has-error').removeClass('has-error');
            block.find('.help-block.input-error').remove();
            if (response.status !== 200) {
                response.json().then(function(data) {
                    let message = data.hasOwnProperty('message') ? data.message :
                        "<?php echo app('translator')->get('Ops,something went wrong !'); ?>"

                    notify("<?php echo app('translator')->get('Error'); ?>", message, 'error');

                    Object.keys(data.errors).forEach(function(e) {
                        let inputName = convertInputDotedNameToHtmlArray(e);
                        let input = form.find(`[name="${inputName}[]"]`);
                        if (input.length < 1) {
                            input = form.find(`[name="${inputName}"]`);
                        }
                        input.addClass('has-error error');
                        input.parent().find(".select2 .select2-selection").css({
                            borderColor: "#ea5455"
                        });
                        input.parent().append(
                            `<span class="help-block input-error text-danger">${data.errors[e][0]}</span>`
                        )
                    });
                    $('.content-body .nav-link').removeClass('text-danger').find('i').remove();
                    $('.input-error').each(function(i, el) {
                        let tabs = $(el).parents('.tab-pane');
                        let tabName = tabs.attr('id');
                        let tab = $(`[href*="#${tabName}"]`);
                        if (!tab.hasClass('text-danger')) {
                            tab.addClass('text-danger').append(
                                `<i class="ml-1 icon-exclamation"></i>`);
                        }
                        if (tab.parents('.tab-pane').attr('id')) {
                            tabName = tab.parents('.tab-pane').attr('id');
                            tab = $(`[href*="#${tabName}"]`);
                            if (!tab.hasClass('text-danger')) {
                                tab.addClass('text-danger').append(
                                    `<i class="ml-1 icon-exclamation"></i>`);
                            }
                        }
                    })
                });
                return;
            }
            notify("<?php echo app('translator')->get('Success'); ?>", message, 'success');
            response.json().then(function(data) {
                let callback = el.data('callback');
                if (callback) {
                    window[callback](data);
                }
                console.log(el.data("refresh"));
                if (el.data('disable-redirect') !== true) {
                    if (data['redirect'] === true) {
                        setTimeout(() => window.location.href = data["url"])
                    }
                    if (el.data('redirect-url') != null) {
                        setTimeout(function() {
                            window.location.href = el.data('redirect-url');
                        });
                    }
                }
            });
        });
        request.catch(function(err) {
            console.log(err);
        });
    });
    $('body').on('click', '.confirm', function(e) {
        let msg = $(this).data("msg-text")
        msg ??= "<?php echo app('translator')->get('Are you sure?'); ?>";
        if (!window.confirm(msg)) {
            e.preventDefault();
        }
    })
    $('[data-user-setting]').on('click', function() {
        let data = $(this).data('user-setting');
        $.ajax({
            url: route('admin.update.user.settings'),
            method: 'POST',
            data: {
                data: data,
            }
        });
    })
    // for remove any list has sub empty list item in menu
    $("li.has-sub").each(function() {
        if ($(this).find("ul > li").length === 0) {
            $(this).remove();
        }
    });
    $('.navigation-accordion a').click(function(e) {
        if (document.readyState !== 'complete') {
            e.preventDefault();
        }
    });
    $(".filter-country-select").on("change", function() {
        let country = $(this).val();
        let select = $(".filter-city-select");
        select.empty().attr("disabled", "disabled");
        $.ajax({
            url: route("admin.countries.country.cities", country),
            success: function(data) {
                select.html(data).removeAttr("disabled");
            }
        });
    });
    $(function() {
        setTimeout(function() {
            let lazyViews = $('[data-lazy-view]');
            let xhr;
            lazyViews.each(function(i, el) {
                xhr = $.ajax({
                    url: $(el).data('lazy-view'),
                    data: $(el).data('lazy-view-data'),
                    success: function(data) {
                        $(el).html(data);
                        initFeather();
                        $('a[data-action="collapse"]').on('click', function(e) {
                            e.preventDefault();
                            $(this).closest('.card').children(
                                '.card-content').collapse('toggle');
                            $(this).closest('.card').find(
                                '[data-action="collapse"]').toggleClass(
                                'rotate');
                        });
                    }
                });
            })
        });
    });
</script>
<?php echo $__env->yieldPushContent('scripts'); ?>
<?php echo $__env->yieldPushContent('end_scripts'); ?>
<?php /**PATH D:\Workstation\Taswk\medhal\Packages\admin-theme-vuexy\src/views/parts/footer.blade.php ENDPATH**/ ?>