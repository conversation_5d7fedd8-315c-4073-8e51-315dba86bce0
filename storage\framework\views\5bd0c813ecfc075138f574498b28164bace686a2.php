<?php $attributes = $attributes->exceptProps([
'route' => '',
'id' => '',

]); ?>
<?php foreach (array_filter(([
'route' => '',
'id' => '',

]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>


<a class="btn btn-icon btn-sm btn-outline-success waves-effect mr-1" href="<?php echo e(route($route,$id)); ?>">
    <i data-feather='edit'></i>
</a>
<?php /**PATH D:\Workstation\Taswk\medhal\Packages\admin-theme-vuexy\src/views/components/list-item-edit.blade.php ENDPATH**/ ?>