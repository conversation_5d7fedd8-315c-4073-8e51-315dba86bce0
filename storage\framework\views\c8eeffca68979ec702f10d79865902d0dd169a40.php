<div class="card">
    <div class="card-header">
        <h4 class="card-title"><?php echo app('translator')->get('Subscription data'); ?></h4>
        <div class="heading-elements">
            <ul class="list-inline mb-0">
                <li>
                    <a data-action="collapse">
                        <i data-feather="chevron-down"></i>
                    </a>
                </li>
            </ul>
        </div>
    </div>
    <div class="card-content collapse show">
        <div class="card-body">
            <ul class="list-group">
                <li class="list-group-item  d-flex justify-content-between align-items-center">
                    <span> <?php echo app('translator')->get('ID'); ?>.</span>
                    <span><?php echo e($subscription->id); ?></span>
                </li>
                <li class="list-group-item  d-flex justify-content-between align-items-center">
                    <span> <?php echo app('translator')->get('User name'); ?>.</span>
                    <span>
                        <a target="blank"
                            href="<?php echo e(route('admin.crm.customers.show', optional($subscription->user)->id)); ?>">
                            <?php echo e(optional($subscription->user)->name); ?>

                        </a>
                    </span>
                </li>
                <?php if(isset($subscription['data']->name)): ?>
                    <li class="list-group-item  d-flex justify-content-between align-items-center">
                        <span> <?php echo app('translator')->get('Trainee name'); ?>.</span>
                        <span>
                            <?php echo e($subscription['data']->name); ?>

                        </span>
                    </li>
                <?php endif; ?>
                <?php if(isset($subscription['data']->age)): ?>
                    <li class="list-group-item  d-flex justify-content-between align-items-center">
                        <span> <?php echo app('translator')->get('Trainee age'); ?>.</span>
                        <span>
                            <?php echo e($subscription['data']->age); ?>

                        </span>
                    </li>
                <?php endif; ?>
                <?php if(isset($subscription['data']->gender)): ?>
                    <li class="list-group-item  d-flex justify-content-between align-items-center">
                        <span> <?php echo app('translator')->get('Trainee gender'); ?>.</span>
                        <span>
                            <?php echo e(__(Str::headline($subscription['data']->gender))); ?>

                        </span>
                    </li>
                <?php endif; ?>
                <li class="list-group-item  d-flex justify-content-between align-items-center">
                    <span> <?php echo app('translator')->get('Status'); ?>.</span>
                    <span>
                        
                        <?php echo app('translator')->get(Str::headline($subscription->status)); ?>
                        
                    </span>
                </li>
                <li class="list-group-item  d-flex justify-content-between align-items-center">
                    <span> <?php echo app('translator')->get('Classification'); ?>.</span>
                    <span><?php echo e($subscription->classification->title); ?></span>
                </li>
                <?php if($subscription->trainer): ?>
                    <li class="list-group-item  d-flex justify-content-between align-items-center">
                        <span> <?php echo app('translator')->get('Trainer'); ?>.</span>
                        <span>
                            <a href="<?php echo e(route('admin.trainers.edit', $subscription->trainer->id)); ?>">
                                <?php echo e($subscription->trainer->name); ?>

                            </a>
                        </span>
                    </li>
                <?php endif; ?>
                <?php if($subscription->type == 'in_center' && $subscription->plan_data != null): ?>
                    <li class="list-group-item  d-flex justify-content-between align-items-center">
                        <span> <?php echo app('translator')->get('Created at'); ?>.</span>
                        <span><?php echo e($subscription->start_date ?? ''); ?></span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span> <?php echo app('translator')->get('Expires at'); ?></span>
                        <span><?php echo e($subscription->end_date ?? ''); ?></span>
                    </li>
                <?php else: ?>
                    <li class="list-group-item  d-flex justify-content-between align-items-center">
                        <span> <?php echo app('translator')->get('Created at'); ?>.</span>
                        <span><?php echo e($subscription->created_at); ?></span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span> <?php echo app('translator')->get('Expires at'); ?></span>
                        <span><?php echo e($subscription->expires_at ?? ''); ?></span>
                    </li>
                <?php endif; ?>
                <?php if($subscription->type == 'online' && $subscription->payment_data == 'in_center'): ?>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span> <?php echo app('translator')->get('Collection method'); ?></span>
                        <span><?php echo e(__(Str::headline($subscription->collection_method)) ?? ''); ?></span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <span> <?php echo app('translator')->get('Payment confirmation official'); ?></span>
                        <span><?php echo e($subscription->PaymentOwner->name ?? ''); ?></span>
                    </li>
                    <li class="list-group-item  d-flex justify-content-between align-items-center">
                        <span> <?php echo app('translator')->get('Invoice image'); ?>.</span>
                        <span>
                            <?php if($subscription->getFirstMediaUrl('payment_image')): ?>
                                <img style="width: 100px; height: 60px;"
                                    src="<?php echo e($subscription->getFirstMediaUrl('payment_image')); ?>" alt=""
                                    srcset="">
                            <?php endif; ?>
                        </span>
                    </li>
                <?php elseif($subscription->type == 'in_center'): ?>
                    <li class="list-group-item  d-flex justify-content-between align-items-center">
                        <span> <?php echo app('translator')->get('Invoice image'); ?>.</span>
                        <span>
                            <?php if($subscription->getFirstMediaUrl('invoice_image')): ?>
                                <img style="width: 100px; height: 60px;"
                                    src="<?php echo e($subscription->getFirstMediaUrl('invoice_image')); ?>" alt=""
                                    srcset="">
                            <?php endif; ?>
                        </span>
                    </li>
                <?php else: ?>
                    <li class="list-group-item  d-flex justify-content-between align-items-center">
                        <span> <?php echo app('translator')->get('Invoice url'); ?>.</span>
                        <span>

                            <a target="blank" href="<?php echo e($subscription->payment_data['invoiceURL'] ?? ''); ?>">
                                <?php echo app('translator')->get('Show invoice'); ?>
                            </a>

                        </span>
                    </li>
                <?php endif; ?>

            </ul>
        </div>
    </div>

</div>
<div class="card">
    <div class="card-header">
        <h4 class="card-title"><?php echo app('translator')->get('Subscription totals'); ?></h4>
        <div class="heading-elements">
            <ul class="list-inline mb-0">
                <li>
                    <a data-action="collapse">
                        <i data-feather="chevron-down"></i>
                    </a>
                </li>
            </ul>
        </div>
    </div>
    <?php if($subscription->type == 'in_center' && $subscription->plan_data != null): ?>
        <div class="card-content collapse show">
            <div class="card-body">
                <ul class="list-group">
                    <li class="list-group-item  d-flex justify-content-between align-items-center">
                        <span> <?php echo app('translator')->get('Cost'); ?>.</span>
                        <span><?php echo e($subscription->cost ?? 0); ?></span>
                    </li>
                    <li class="list-group-item  d-flex justify-content-between align-items-center">
                        <span> <?php echo app('translator')->get('Discount'); ?>.</span>
                        <span>
                            <?php echo e($subscription->discount ?? 0); ?>

                        </span>
                    </li>
                    <li class="list-group-item  d-flex justify-content-between align-items-center">
                        <span> <?php echo app('translator')->get('Cost after discount'); ?>.</span>
                        <span>
                            <?php echo e($subscription->cost_after_discount ?? 0); ?>

                        </span>
                    </li>
                    <li class="list-group-item  d-flex justify-content-between align-items-center">
                        <span> <?php echo app('translator')->get('Taxes'); ?>.</span>
                        <span>
                            <?php echo e($subscription->taxes ?? 0); ?>

                        </span>
                    </li>
                    <li class="list-group-item  d-flex justify-content-between align-items-center">
                        <span> <?php echo app('translator')->get('Cost after taxes'); ?>.</span>
                        <span>
                            <?php echo e($subscription->cost_after_taxes ?? 0); ?>

                        </span>
                    </li>



                </ul>
            </div>
        </div>
    <?php else: ?>
        <?php ($totals = $subscription->as_cart->formattedTotals()); ?>
        <div class="card-content collapse show">
            <div class="card-body">
                <ul class="list-group">
                    <li class="list-group-item  d-flex justify-content-between align-items-center">
                        <span> <?php echo app('translator')->get('Plan cost'); ?>.</span>
                        <span><?php echo e($totals['items_total_without_vat']); ?></span>
                    </li>
                    <li class="list-group-item  d-flex justify-content-between align-items-center">
                        <span> <?php echo app('translator')->get('Discount'); ?>.</span>
                        <span>
                            <?php echo e($totals['discount']); ?>

                        </span>
                    </li>
                    <li class="list-group-item  d-flex justify-content-between align-items-center">
                        <span> <?php echo app('translator')->get('Taxes'); ?>.</span>
                        <span>
                            <?php echo e($totals['taxes']); ?>

                        </span>
                    </li>
                    <li class="list-group-item  d-flex justify-content-between align-items-center">
                        <span> <?php echo app('translator')->get('Total'); ?>.</span>
                        <span>
                            <?php echo e($totals['total']); ?>

                        </span>
                    </li>



                </ul>
            </div>
        </div>
    <?php endif; ?>
</div>
<?php /**PATH D:\Workstation\Taswk\medhal\Packages\subscriptions\src/../resources/views/themes/vuexy/components/basic-data.blade.php ENDPATH**/ ?>