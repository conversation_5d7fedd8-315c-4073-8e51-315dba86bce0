
<?php $__env->startSection('head_title', __('Subscriptions')); ?>
<?php $__env->startSection('title', __('New Subscription')); ?>
<?php $__env->startSection('content'); ?>

    <div class="card-body table-responsive">
        <form class="form-horizontal new-subscription-form" method="post"
              action="<?php echo e(route('admin.subscription.update',$subscription->id)); ?>"
              enctype="multipart/form-data">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PUT'); ?>
            <div class="row" data-panel-id="new-subscription">
                <div class="col-md-12">
                    <div class="card" data-panel-id="new-subscription">
                        <div class="card-header">
                            <h6 class="card-title"><?php echo app('translator')->get('Subscription data'); ?> </h6>
                            <div class="heading-elements">
                                <ul class="list-inline mb-0">
                                    <li>
                                        <a data-action="collapse"
                                           data-user-setting='{"admin.subscription.basic-data-panel":"<?php echo e(setting_user()->get('admin.subscription.basic-data-panel') == 'true' ? 'false' : 'true'); ?>"}'>
                                            <?php if(setting_user()->get('admin.subscription.basic-data-panel') == 'true'): ?>
                                                <i data-feather="chevron-down"></i>
                                            <?php else: ?>
                                                <i data-feather="chevron-up"></i>
                                            <?php endif; ?>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="card-content collapse show">

                            <div class="card-body">
                                <div class="row">
                                    <div class="form-group col-md-12 ">
                                        

                                        <label class="control-label label_required"><?php echo app('translator')->get('Customer'); ?></label>
                                        <select class="select-search form-control" data-style="btn-default btn-lg"
                                                data-width="100%" data-placeholder="<?php echo app('translator')->get('Choose'); ?>" name="user_id">
                                            <option value=""></option>
                                            <?php $__currentLoopData = $customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option <?php if($subscription->user_id == $customer->id): ?> selected   <?php endif; ?> value="<?php echo e($customer->id); ?>"><?php echo e($customer->name); ?>

                                                    ( <?php echo e($customer->phone); ?>)
                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                        <?php $__errorArgs = ['user_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <span class="text-danger"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label class="control-label label_required"><?php echo app('translator')->get('Plan type'); ?></label>
                                        <select class="select-search form-control type-select" data-width="100%"
                                                data-placeholder="<?php echo app('translator')->get('Choose'); ?>" name="plan_type">
                                            <option></option>
                                            <option
                                                <?php if($subscription->plan->type == 'Little_ones'): ?> <?php endif; ?>  value="Little_ones"><?php echo e(__('Little ones')); ?></option>
                                            <option <?php if($subscription->plan->type == 'Kids'): ?> selected
                                                    <?php endif; ?> value="Kids"><?php echo e(__('Kids')); ?></option>
                                            <option <?php if($subscription->plan->type == 'Adults'): ?> selected
                                                    <?php endif; ?> value="Adults"><?php echo e(__('Adults')); ?></option>
                                        </select>
                                    </div>

                                    <div class="form-group col-md-6">
                                        <?php echo e(html()->label(trans('Plan'))->class('control-label label_required')); ?>

                                        <?php echo e(html()->select('plan_id')->class('select-search form-control plan-select')->data('placeholder', trans('Select'))->value($subscription->plan_id)->options(\Tasawk\Cms\Model\Plan::ListsTranslations('name')->where('type', $subscription->plan->type)->pluck('name', 'id'))); ?>

                                    </div>

                                    <div class="form-group col-md-6">
                                        <label class="control-label label_required"><?php echo app('translator')->get('Specialization'); ?></label>
                                        <select class="select-search form-control specialization-select"
                                                data-width="100%"
                                                data-placeholder="<?php echo app('translator')->get('Choose'); ?>" name="specialization_id">
                                            <option value=""></option>
                                            <?php $__currentLoopData = $specializations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $specialization): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option <?php if($subscription->classification_id == $key): ?> selected <?php endif; ?> value="<?php echo e($key); ?>"><?php echo e($specialization); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>


                                        </select>
                                    </div>
                                    <div class="form-group col-md-6">
                                        <?php echo e(html()->label(trans('Trainer'))->class('control-label label_required')); ?>

                                        <?php echo e(html()->select('trainer_id')->class('select-search form-control trainer-select')
->value($subscription->trainer_id)
->data('placeholder', trans('Select'))->options(
    \Tasawk\Employees\Models\Trainer::listsTranslations("name")->pluck('name', 'id')
)); ?>

                                    </div>

                                    <div class="form-group col-md-6">
                                        <label class="control-label"><?php echo app('translator')->get('Subscribe date'); ?></label>
                                        <input type="date" class="form-control" id="start_date" name="start_date"
                                               placeholder="<?php echo e(__('Subscribe date')); ?>"
                                               value="<?php echo e($subscription?->start_date??$subscription->created_at->format("Y-m-d")); ?>"
                                        >
                                        <?php $__errorArgs = ['start_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <span class="text-danger"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label class="control-label"><?php echo app('translator')->get('End subscribe date'); ?></label>
                                        <input type="date" class="form-control" id="end_date" name="end_date"
                                               value="<?php echo e($subscription->expires_at->format("Y-m-d")); ?>"
                                               placeholder="<?php echo e(__('End subscribe date')); ?>">
                                        <?php $__errorArgs = ['end_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <span class="text-danger"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label class="control-label"><?php echo app('translator')->get('Subscription Sessions'); ?></label>
                                        <input type="number" class="form-control"
                                               name="subscription_sessions_count" placeholder="<?php echo e(__('Subscription Sessions')); ?>" value="<?php echo e($subscription->plan_data->sessions??0); ?>">
                                        <?php $__errorArgs = ['subscription_sessions_count'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <span class="text-danger"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label class="control-label"><?php echo app('translator')->get('Remaining sessions'); ?></label>
                                        <input type="number" class="form-control" id="remaining sessions"
                                               name="remaining_sessions" placeholder="<?php echo e(__('Remaining sessions')); ?>" value="<?php echo e($subscription->remaining_quota['sessions']??0); ?>">
                                        <?php $__errorArgs = ['remaining_sessions'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <span class="text-danger"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label class="control-label"><?php echo app('translator')->get('Cost'); ?></label>
                                        <input type="number" class="form-control" id="cost" name="cost"
                                               placeholder="<?php echo e(__('Cost')); ?>"
                                               value="<?php echo e(max($subscription->total,$subscription->cost)); ?>"
                                        >
                                        <?php $__errorArgs = ['cost'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <span class="text-danger"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label class="control-label"><?php echo app('translator')->get('Discount'); ?></label>
                                        <input type="number" class="form-control" id="discount" name="discount"
                                               placeholder="<?php echo e(__('Discount')); ?>" value="<?php echo e($subscription->discount); ?>">
                                        <?php $__errorArgs = ['discount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <span class="text-danger"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label class="control-label"><?php echo app('translator')->get('Cost after discount'); ?></label>
                                        <input type="number" class="form-control" id="cost_after_discount"
                                               name="cost_after_discount" placeholder="<?php echo e(__('Cost after discount')); ?>"
                                               value="<?php echo e($subscription->cost_after_discount); ?>"
                                        >
                                        <?php $__errorArgs = ['cost_after_discount'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <span class="text-danger"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label class="control-label"><?php echo app('translator')->get('Taxes'); ?></label>
                                        <input type="number" class="form-control" id="taxes" name="taxes"
                                               placeholder="<?php echo e(__('Taxes')); ?>"
                                               value="<?php echo e($subscription->taxes); ?>"
                                        >
                                        <?php $__errorArgs = ['taxes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <span class="text-danger"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>

                                    <div class="form-group col-md-6">
                                        <label class="control-label"><?php echo app('translator')->get('Cost after taxes'); ?></label>
                                        <input type="number" class="form-control" id="cost_after_taxes"
                                               name="cost_after_taxes"
                                               placeholder="<?php echo e(__('Cost after taxes')); ?>" value="<?php echo e($subscription->cost_after_taxes); ?>">
                                        <?php $__errorArgs = ['cost_after_taxes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <span class="text-danger"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    <div class="form-group col-md-12">
                                        <label class="control-label"><?php echo e(__('Invoice image')); ?></label>
                                        <input type="hidden" name="invoice_image">
                                        <input type="file" class="file-input" name="invoice_image"
                                               data-default-file="<?php echo e($subscription->getFirstMediaUrl('invoice_image')); ?>"
                                               data-show-caption="true" data-show-upload="false" accept="image/*"
                                               placeholder="<?php echo e(__('Image')); ?>">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="card" data-panel-id="new-subscription">
                        <div class="card-header">
                            <h6 class="card-title"><?php echo app('translator')->get('Subscriber data'); ?> </h6>
                            <div class="heading-elements">
                                <ul class="list-inline mb-0">
                                    <li>
                                        <a data-action="collapse"
                                           data-user-setting='{"admin.subscription.basic-data-panel":"<?php echo e(setting_user()->get('admin.subscription.basic-data-panel') == 'true' ? 'false' : 'true'); ?>"}'>
                                            <?php if(setting_user()->get('admin.subscription.basic-data-panel') == 'true'): ?>
                                                <i data-feather="chevron-down"></i>
                                            <?php else: ?>
                                                <i data-feather="chevron-up"></i>
                                            <?php endif; ?>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="card-content collapse show">
                            <div class="card-body">
                                <div class="form-group">
                                    <label class="control-label label_required"><?php echo app('translator')->get('Trainee name'); ?> :</label>
                                    <input type="text" class="form-control" value="<?php echo e(old('name',$subscription->data->name??'')); ?>"
                                           name="name" placeholder="<?php echo e(__('Trainee name')); ?>">
                                    <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="text-danger"><?php echo e($message); ?></span>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="form-group">
                                    <label class="control-label label_required"><?php echo app('translator')->get('Trainee gender'); ?>:</label>
                                    <select class="select-search form-control" data-style="btn-default btn-lg"
                                            data-width="100%" data-placeholder="<?php echo app('translator')->get('Choose'); ?>" name="gender">
                                        <option value=""></option>
                                        <option <?php if($subscription?->data?->gender =='male'): ?> selected <?php endif; ?> value="male"><?php echo e(__('Male')); ?></option>
                                        <option <?php if($subscription?->data?->gender =='female'): ?> selected <?php endif; ?> value="female"><?php echo e(__('Female')); ?></option>
                                    </select>
                                    <?php $__errorArgs = ['gender'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="text-danger"><?php echo e($message); ?></span>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                                <div class="form-group">
                                    <label class="control-label label_required"><?php echo app('translator')->get('Trainee age'); ?> :</label>
                                    <input type="number" class="form-control" value="<?php echo e(old('age',$subscription->data?->age)); ?>"
                                           name="age" placeholder="<?php echo e(__('Trainee age')); ?>" >
                                    <?php $__errorArgs = ['age'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <span class="text-danger"><?php echo e($message); ?></span>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <button data-has-ajax data-ajax-form=".new-subscription-form"
                            data-ajax-url="<?php echo e(route('admin.subscription.store')); ?>"
                            data-ajax-ui-block='[data-panel-id="new-subscription"]'
                            data-redirect-url="<?php echo e(route('admin.subscription.index')); ?>" class="btn btn-primary">
                        <?php echo app('translator')->get('Save'); ?>
                    </button>
                </div>
            </div>
        </form>
    </div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('subscriptions::Dashboard.modals.new_customer_modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->startPush('scripts'); ?>
    <script>
        $('#customer').on('change', function() {

            var selectVal = $("#customer option:selected").val();

            if (selectVal == 'new') {
                $('.newCustomer').removeClass('d-none');
                $('.oldCustomer').addClass('d-none');
            }
            if (selectVal == 'old') {
                $('.oldCustomer').removeClass('d-none');
                $('.newCustomer').addClass('d-none');
            }
        });
    </script>
    <script>
        $(function() {
            $(document).on("change", ".zone-select", function() {
                let zone = $(this).val();
                let select = $(".city-select");
                select.empty().attr("disabled", "disabled");
                $.ajax({
                    url: route("admin.zones.zone.cities", zone),
                    success: function(data) {
                        select.html(data).removeAttr("disabled");
                    }
                });
            });
        });
        $(function() {
            $(document).on("change", ".type-select", function() {
                let type = $(this).val();

                let select = $(".plan-select");
                select.empty().attr("disabled", "disabled");
                $.ajax({
                    url: route("admin.type.plans", type),
                    success: function(data) {
                        select.html(data).removeAttr("disabled");
                    }
                });
            });
        });
        $(function() {
            $(document).on("change", ".specialization-select", function() {
                let specialization = $(this).val();

                let select = $(".trainer-select");
                select.empty().attr("disabled", "disabled");
                $.ajax({
                    url: route("admin.specialization.trainers", specialization),
                    success: function(data) {
                        select.html(data).removeAttr("disabled");
                    }
                });
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin::layouts.2_col', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Workstation\Taswk\medhal\Packages\subscriptions\src/../resources/views/themes/vuexy/Dashboard/edit.blade.php ENDPATH**/ ?>