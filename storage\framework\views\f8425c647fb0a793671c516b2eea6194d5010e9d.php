<?php
    $result = '';
    $data = \TorMorten\Eventy\Facades\Eventy::filter('admin.help.route.data', []);
    $currentRouteName = \Route::currentRouteName();
    if (isset($data[$currentRouteName])) {
        $result = $data[$currentRouteName];
    }
?>
<?php if($result != ''): ?>
    <li class="nav-item d-none d-lg-block" data-toggle="modal" data-target="#help" title="<?php echo e(__('Help')); ?>">
        <a href="javascript:void(0)" class="nav-link">
            <i class="icon-help"></i>
        </a>
    </li>
<?php endif; ?><?php /**PATH D:\Workstation\Taswk\medhal\Packages\utilities\src/../resources/views/themes/vuexy/help/index.blade.php ENDPATH**/ ?>