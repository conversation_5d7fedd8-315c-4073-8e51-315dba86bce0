
<?php if($subscription->user->isRated($subscription)): ?>
    <?php ($rate= $subscription->user->rated($subscription)->first()); ?>
    <div class="card">
        <div class="card-header">
            <h4 class="card-title"><?php echo app('translator')->get('Subscription rate'); ?></h4>
            <div class="heading-elements">
                <ul class="list-inline mb-0">
                    <li>
                        <a data-action="collapse">
                            <i data-feather="chevron-down"></i>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
        <div class="card-content collapse show">
            <div class="card-body table-responsive">
                <ul class="list-group">
                    <li class="list-group-item  d-flex justify-content-between align-items-center">
                        <span> <?php echo app('translator')->get('Comment'); ?>.</span>
                        <span><?php echo e($rate->comment); ?></span>
                    </li>
                    <li class="list-group-item  d-flex justify-content-between align-items-center">
                        <span> <?php echo app('translator')->get('Stars'); ?></span>
                        <span><?php echo e($rate->stars); ?></span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
<?php endif; ?>
<?php /**PATH D:\Workstation\Taswk\medhal\Packages\subscriptions\src/../resources/views/themes/vuexy/components/subscription-rate.blade.php ENDPATH**/ ?>