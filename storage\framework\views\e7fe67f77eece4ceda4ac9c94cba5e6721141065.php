<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
<base href="<?php echo e(AdminBase::theTheme()->asset()); ?>">
<link rel="apple-touch-icon" sizes="180x180" href="app-assets/icons/apple-touch-icon.png">
<link rel="icon" type="image/png" sizes="16x16" href="<?php echo e(display_logo_avatar()); ?>">
<link rel="manifest" href="app-assets/icons/site.webmanifest">
<link rel="mask-icon" href="app-assets/icons/safari-pinned-tab.svg" color="#5bbad5">
<meta name="msapplication-TileColor" content="#da532c">
<meta name="theme-color" content="#fe7022">
<title>
    <?php if (! empty(trim($__env->yieldContent('head_title')))): ?>
        <?php echo $__env->yieldContent('head_title'); ?>
        -
    <?php endif; ?> <?php echo e(settings()->group('default')->get('company_name', config('app.name'))); ?>

</title>
<?php echo $__env->make('admin::parts.head.assets', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo app('Tightenco\Ziggy\BladeRouteGenerator')->generate('admin'); ?>
<?php if(app()->getLocale() == 'ar'): ?>
    <style>
        .vertical-layout.vertical-menu-modern.menu-expanded .main-menu .navigation li.has-sub.open:not(.menu-item-closing)>a:after {
            transform: rotate(90deg);
        }

        .vertical-layout.vertical-menu-modern.menu-expanded .main-menu .navigation li.has-sub>a:after {
            content: '';
            background-image: url(data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%236e6b7b\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-right\'%3E%3Cpolyline points=\'9 18 15 12 9 6\'%3E%3C/polyline%3E%3C/svg%3E);
            background-repeat: no-repeat;
            background-position: center;
            background-size: 1.1rem;
            height: 1.1rem;
            width: 1.1rem;
            display: inline-block;
            position: absolute;
            left: 20px;
            top: 14px;
            transform: rotate(180deg);
            transition: all .2s ease-out;
        }
    </style>
<?php endif; ?>
<?php app('eventy')->action('admin.head'); ?>
<?php /**PATH D:\Workstation\Taswk\medhal\Packages\admin-theme-vuexy\src/views/parts/head.blade.php ENDPATH**/ ?>