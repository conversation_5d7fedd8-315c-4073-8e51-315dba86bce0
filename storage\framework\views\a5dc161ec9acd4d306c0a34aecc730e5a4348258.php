<!DOCTYPE html>
<html class="loading"
      lang="<?php echo e(app()->getLocale()); ?>"
      dir="<?php echo e(app()->getLocale() == 'ar' ? 'rtl' : 'ltr'); ?>"
      data-textdirection="<?php echo e(app()->getLocale() == 'ar' ? 'rtl' : 'ltr'); ?>">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
    <base href="<?php echo e(AdminBase::theTheme()->asset('')); ?>">
    <meta name="author" content="Medhal">
    <title><?php echo app('translator')->get('Login page'); ?> - <?php echo app('translator')->get('Dashboard'); ?></title>
    <style>
        .auth-wrapper.auth-v1 .auth-inner:before, .auth-wrapper.auth-v1 .auth-inner:after {
            /*background-image: url('/assets/office/site/images/footer/logo.svg') !important;*/
            background-image: none !important;
            background-repeat: no-repeat;
            opacity: 0.1;
        }

        .langButton {
            position: absolute;
            top: 0px;
            /*background: #4977B6;*/
            color: white;
            padding: 10px;
            font-family: system-ui;
        }

        .langButton:hover {
            color: black;
        }
    </style>
    <?php if(app()->getLocale() == "en"): ?>
        <link
            href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,300;0,400;0,500;0,600;1,400;1,500;1,600"
            rel="stylesheet">
        <style>
            .langButton {
                border-bottom-left-radius: 7px;
                right: 0px;
            }
        </style>
    <?php else: ?>
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;600;700;900&display=swap"
              rel="stylesheet">
        <style>
            body {
                font-family: 'Cairo', sans-serif !important;
            }

            .langButton {
                border-bottom-right-radius: 7px;
                left: 0px;
            }
        </style>
    <?php endif; ?>
    <link rel="stylesheet" type="text/css"
          href="app-assets/vendors/css/vendors<?php echo e((app()->getLocale() == 'ar') ? '-rtl': ''); ?>.min.css">
    <link rel="stylesheet" type="text/css"
          href="app-assets/css<?php echo e((app()->getLocale() == 'ar') ? '-rtl': ''); ?>/bootstrap.css">
    <link rel="stylesheet" type="text/css"
          href="app-assets/css<?php echo e((app()->getLocale() == 'ar') ? '-rtl': ''); ?>/bootstrap-extended.css">
    <link rel="stylesheet" type="text/css"
          href="app-assets/css<?php echo e((app()->getLocale() == 'ar') ? '-rtl': ''); ?>/colors.css">
    <link rel="stylesheet" type="text/css"
          href="app-assets/css<?php echo e((app()->getLocale() == 'ar') ? '-rtl': ''); ?>/components.css">
    <link rel="stylesheet" type="text/css"
          href="app-assets/css<?php echo e((app()->getLocale() == 'ar') ? '-rtl': ''); ?>/themes/dark-layout.css">
    <link rel="stylesheet" type="text/css"
          href="app-assets/css<?php echo e((app()->getLocale() == 'ar') ? '-rtl': ''); ?>/themes/bordered-layout.css">
    <link rel="stylesheet" type="text/css"
          href="app-assets/css<?php echo e((app()->getLocale() == 'ar') ? '-rtl': ''); ?>/core/menu/menu-types/vertical-menu.css">
    <link rel="stylesheet" type="text/css"
          href="app-assets/css<?php echo e((app()->getLocale() == 'ar') ? '-rtl': ''); ?>/plugins/forms/form-validation.css">
    <link rel="stylesheet" type="text/css"
          href="app-assets/css<?php echo e((app()->getLocale() == 'ar') ? '-rtl': ''); ?>/pages/page-auth.css">
    <link rel="stylesheet" type="text/css"
          href="app-assets/css<?php echo e((app()->getLocale() == 'ar') ? '-rtl': ''); ?>/custom<?php echo e((app()->getLocale() == 'ar') ? '-rtl': ''); ?>.css">
    <link rel="stylesheet" type="text/css" href="assets/css/style<?php echo e((app()->getLocale() == 'ar') ? '-rtl': ''); ?>.css">
    <style>
        .btn-primary {
            border-color: #7367f0 !important;
            background-color: #7367f0 !important;
        }

        .btn-primary:hover:not(.disabled):not(:disabled) {
            box-shadow: 0 8px 25px -8px #7367f0;
        }

        .btn-primary:focus, .btn-primary:active, .btn-primary.active {
            background-color: #7367f0 !important;
        }
    </style>
</head>
<body class="vertical-layout vertical-menu-modern blank-page navbar-floating footer-static  " data-open="click"
      data-menu="vertical-menu-modern" data-col="blank-page">
<div class="app-content content">
    <div class="content-overlay"></div>
    <div class="header-navbar-shadow"></div>
    <div class="content-wrapper">
        <div class="content-header row">
        </div>
        <div class="content-body">
            <?php ( $convertLang = app()->getLocale() == "en"  ? "ar" : "en"); ?>








            <div class="auth-wrapper auth-v1 px-2">
                <div class="auth-inner py-2">
                    <div class="card mb-0">
                        <div class="card-body">
                            <a href="javascript:void(0);" class="brand-logo">
                                <img class="width-150" src="<?php echo e(upload_storage_url(settings()->group('default')->get('logo'))); ?>" alt="logo">
                            </a>
                            <h4 class="card-title mb-1"><?php echo app('translator')->get('Welcome back !'); ?></h4>
                            <p class="card-text mb-2"><?php echo app('translator')->get('Please sign-in to your account'); ?></p>
                            <?php echo e(html()->form('POST',route('login'))->class('auth-login-form mt-2')->open()); ?>

                            <div class="form-group">
                                <label class="form-label" for="login-email"><?php echo app('translator')->get('Email Or Phone'); ?></label>
                                <input
                                    <?php if(request()->getHost() == env('DEMO_ACCOUNT_HOST')): ?> value="<?php echo e(env('DEMO_ACCOUNT_EMAIL')); ?>"
                                    <?php endif; ?>
                                    dir="ltr" class="form-control" id="login-email" type="text" name="identifier"
                                     aria-describedby="login-email" autofocus=""
                                    tabindex="1"/>
                            </div>
                            
                            
                            
                            
                            <div class="form-group">
                                <div class="d-flex justify-content-between">
                                    <label for="login-password"><?php echo app('translator')->get('Password'); ?></label>
                                </div>
                                <div class="input-group input-group-merge form-password-toggle">
                                    <input class="form-control form-control-merge" id="login-password"
                                           <?php if(request()->getHost() == env('DEMO_ACCOUNT_HOST')): ?> value="<?php echo e(env('DEMO_ACCOUNT_PASSWORD')); ?>"
                                           <?php endif; ?>
                                           type="password" name="password"
                                           aria-describedby="login-password" tabindex="2"/>
                                    <div class="input-group-append"><span class="input-group-text cursor-pointer"><i
                                                data-feather="eye"></i></span></div>
                                </div>
                                <?php if(session('error')): ?>
                                    <div class="text-center pt-1">
                                        <small class="display-block text-danger"><?php echo e(session('error')); ?></small>
                                    </div>
                                <?php endif; ?>
                            </div>

                            
                            
                            
                            
                            
                            
                            
                            
                            
                            
                            
                            
                            
                            
                            
                            
                            
                            
                            
                            
                            <div class="form-group">
                                <div class="custom-control custom-checkbox">
                                    <input class="custom-control-input" name="remember-me" value="1" id="remember-me"
                                           type="checkbox" tabindex="3"/>
                                    <label class="custom-control-label" for="remember-me"> <?php echo app('translator')->get('Remember Me'); ?></label>
                                </div>
                            </div>
                            <?php echo e(html()->button(__('Login'),'submit')->class('btn btn-primary btn-block')->attribute('tabindex',4)); ?>


                            <?php echo e(html()->form()->close()); ?>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="app-assets/vendors/js/vendors.min.js"></script>
<script src="app-assets/vendors/js/forms/validation/jquery.validate.min.js"></script>
<script src="app-assets/js/core/app-menu.js"></script>
<script src="app-assets/js/core/app.js"></script>
<script src="app-assets/js/scripts/pages/page-auth-login.js"></script>
<script>
    $(window).on('load', function () {
        if (feather) {
            feather.replace({
                width: 14,
                height: 14
            });
        }
    })
</script>
</body>
</html>
<?php
return;
?>
<!DOCTYPE html>
<html class="loading"
      lang="<?php echo e(app()->getLocale()); ?>"
      dir="<?php echo e(app()->getLocale() == 'ar' ? 'rtl' : 'ltr'); ?>"
      data-textdirection="<?php echo e(app()->getLocale() == 'ar' ? 'rtl' : 'ltr'); ?>">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
    <meta name="description" content="Yassir Dashboard">
    <base href="<?php echo e(AdminBase::theTheme()->asset('')); ?>">
    <meta name="author" content="Yassirhub">
    <title><?php echo app('translator')->get('Login page'); ?> - <?php echo app('translator')->get('Dashboard'); ?></title>
    <link rel="apple-touch-icon" href="app-assets/images/ico/apple-icon-120.png">
    <link rel="shortcut icon" type="image/x-icon" href="app-assets/images/ico/favicon.ico">
    <style>
        .langButton {
            position: absolute;
            top: 0px;
            background: #4977B6;
            color: white;
            padding: 10px;
            font-family: system-ui;
        }

        .langButton:hover {
            color: black;
        }
    </style>
    <?php if(app()->getLocale() == "en"): ?>
        <link
            href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,300;0,400;0,500;0,600;1,400;1,500;1,600"
            rel="stylesheet">
        <style>
            .langButton {
                border-bottom-left-radius: 7px;
                right: 0px;
            }
        </style>
    <?php else: ?>
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;600;700;900&display=swap"
              rel="stylesheet">
        <style>
            body {
                font-family: 'Cairo', sans-serif !important;
            }

            .langButton {
                border-bottom-right-radius: 7px;
                left: 0px;
            }
        </style>
    <?php endif; ?>
    <link rel="stylesheet" type="text/css"
          href="app-assets/vendors/css/vendors<?php echo e((app()->getLocale() == 'ar') ? '-rtl': ''); ?>.min.css">
    <link rel="stylesheet" type="text/css"
          href="app-assets/css<?php echo e((app()->getLocale() == 'ar') ? '-rtl': ''); ?>/bootstrap.css">
    <link rel="stylesheet" type="text/css"
          href="app-assets/css<?php echo e((app()->getLocale() == 'ar') ? '-rtl': ''); ?>/bootstrap-extended.css">
    <link rel="stylesheet" type="text/css"
          href="app-assets/css<?php echo e((app()->getLocale() == 'ar') ? '-rtl': ''); ?>/colors.css">
    <link rel="stylesheet" type="text/css"
          href="app-assets/css<?php echo e((app()->getLocale() == 'ar') ? '-rtl': ''); ?>/components.css">
    <link rel="stylesheet" type="text/css"
          href="app-assets/css<?php echo e((app()->getLocale() == 'ar') ? '-rtl': ''); ?>/themes/dark-layout.css">
    <link rel="stylesheet" type="text/css"
          href="app-assets/css<?php echo e((app()->getLocale() == 'ar') ? '-rtl': ''); ?>/themes/bordered-layout.css">
    <link rel="stylesheet" type="text/css"
          href="app-assets/css<?php echo e((app()->getLocale() == 'ar') ? '-rtl': ''); ?>/core/menu/menu-types/vertical-menu.css">
    <link rel="stylesheet" type="text/css"
          href="app-assets/css<?php echo e((app()->getLocale() == 'ar') ? '-rtl': ''); ?>/plugins/forms/form-validation.css">
    <link rel="stylesheet" type="text/css"
          href="app-assets/css<?php echo e((app()->getLocale() == 'ar') ? '-rtl': ''); ?>/pages/page-auth.css">
    <link rel="stylesheet" type="text/css"
          href="app-assets/css<?php echo e((app()->getLocale() == 'ar') ? '-rtl': ''); ?>/custom<?php echo e((app()->getLocale() == 'ar') ? '-rtl': ''); ?>.css">
    <link rel="stylesheet" type="text/css" href="assets/css/style<?php echo e((app()->getLocale() == 'ar') ? '-rtl': ''); ?>.css">
</head>
<body class="vertical-layout vertical-menu-modern blank-page navbar-floating footer-static" data-open="click"
      data-menu="vertical-menu-modern" data-col="blank-page">
<div class="app-content content ">
    <div class="content-overlay"></div>
    <div class="header-navbar-shadow"></div>
    <div class="content-wrapper">
        <div class="content-header row">
        </div>
        <div class="content-body">
            <div class="auth-wrapper auth-v2">
                <div class="auth-inner row m-0">
                    <a class="brand-logo" href="javascript:void(0);">
                        <?php
                        $logo = '/assets/office/site/images/footer/logo.svg';
                        ?>
                        <img
                            src="<?php echo e($logo); ?>"
                            data-src="<?php echo e((is_null(settings()->group('default')->get('logo'))) ? 'app-assets/images/svg/logo.svg': upload_storage_url(settings()->group('default')->get('logo'))); ?>"
                            style=" width: 150px; ">
                        
                    </a>
                    <div class="d-none d-lg-flex col-lg-8 align-items-center p-5">
                        
                        
                        
                    </div>
                    <div class="d-flex col-lg-12 align-items-center auth-bg">
                        <?php $convertLang = (app()->getLocale() == "en") ? "ar" : "en"; ?>
                        <a class="langButton" hreflang="<?php echo e($convertLang); ?>"
                           href="<?php echo e(LaravelLocalization::getLocalizedURL($convertLang, null, [], true)); ?>"
                           data-language="<?php echo e($convertLang); ?>" class="nav-link a_lang">
                            <?php echo e(app()->getLocale() == "en" ? "عربي" : "English"); ?>

                        </a>
                        <div class="col-12 col-sm-8 col-md-6 col-lg-12 px-xl-2 mx-auto">
                            <h4 class="card-title mb-1"><?php echo app('translator')->get('Welcome back !'); ?></h4>
                            <p class="card-text mb-2"><?php echo app('translator')->get('Please sign-in to your account'); ?></p>
                            <?php echo e(html()->form('POST',route('login'))->open()); ?>

                            <div class="form-group">
                                <label class="form-label" for="login-email"><?php echo app('translator')->get('Email'); ?></label>
                                <input class="form-control" id="login-email" type="text" name="email"
                                       placeholder="<EMAIL>" aria-describedby="login-email" autofocus=""
                                       tabindex="1"/>
                            </div>
                            <div class="form-group">
                                <div class="d-flex justify-content-between">
                                    <label for="login-password"><?php echo app('translator')->get('Password'); ?></label>
                                </div>
                                <div class="input-group input-group-merge form-password-toggle">
                                    <input class="form-control form-control-merge" id="login-password"
                                           type="password" name="password" placeholder="············"
                                           aria-describedby="login-password" tabindex="2"/>
                                    <div class="input-group-append"><span class="input-group-text cursor-pointer"><i
                                                data-feather="eye"></i></span></div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="custom-control custom-checkbox">
                                    <input class="custom-control-input" name="remember-me" value="1" id="remember-me"
                                           type="checkbox" tabindex="3"/>
                                    <label class="custom-control-label" for="remember-me"> <?php echo app('translator')->get('Remember Me'); ?></label>
                                </div>
                            </div>
                            <?php echo e(html()->button(__('Login'),'submit')->class('btn btn-primary btn-block')->attribute('tabindex',4)); ?>

                            <?php if(session('error')): ?>
                                <div class="text-center p-1 position-absolute w-100">
                                    <small class="display-block text-danger"><?php echo e(session('error')); ?></small>
                                </div>
                            <?php endif; ?>
                            <?php echo e(html()->form()->close()); ?>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="app-assets/vendors/js/vendors.min.js"></script>
<script src="app-assets/vendors/js/forms/validation/jquery.validate.min.js"></script>
<script src="app-assets/js/core/app-menu.js"></script>
<script src="app-assets/js/core/app.js"></script>
<script src="app-assets/js/scripts/pages/page-auth-login.js"></script>
<script>
    $(window).on('load', function () {
        if (feather) {
            feather.replace({
                width: 14,
                height: 14
            });
        }
    })
</script>
</body>
</html>
<?php /**PATH D:\Workstation\Taswk\medhal\Packages\admin-theme-vuexy\src/views/auth/login.blade.php ENDPATH**/ ?>