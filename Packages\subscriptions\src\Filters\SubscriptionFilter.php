<?php

namespace Tasawk\Subscriptions\Filters;

use Tasawk\AdminPanel\Lib\Filters\FilterBaseAbstract;
use Tasawk\Subscriptions\Filters\Subscriptions\ClassificationFilter;
use Tasawk\Subscriptions\Filters\Subscriptions\CustomerPhoneFilter;
use Tasawk\Subscriptions\Filters\Subscriptions\CustomersFilter;
use Tasawk\Subscriptions\Filters\Subscriptions\SubscriptionsidFilter;
use Tasawk\Subscriptions\Filters\Subscriptions\PlanFilter;
use Tasawk\Subscriptions\Filters\Subscriptions\TrainerFilter;

class SubscriptionFilter extends FilterBaseAbstract {

    protected $filters = [
        'subscription_id' => SubscriptionsidFilter::class,
        'customer' => CustomersFilter::class,
        'customer_phone' => CustomerPhoneFilter::class,
        'trainer_id' => TrainerFilter::class,
        'plan_id' => PlanFilter::class,
        'plan_id' => PlanFilter::class,
        'classification_id' => ClassificationFilter::class,
    ];
}
