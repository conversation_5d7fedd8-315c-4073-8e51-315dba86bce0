<?php $__env->startPush('modals'); ?>
    <!-- Modal -->
    <div class="modal fade" id="reserveModal" tabindex="-1" aria-labelledby="reserveModalLabel"
         aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="reserveModalLabel"><?php echo e(__('Reserve')); ?></h5>

                </div>
                <div class="modal-body">
                    <form id="reserveFrom" method="post">
                        <?php echo csrf_field(); ?>
                        <?php echo e(html()->hidden('subscription_id')); ?>

                        <?php echo e(html()->hidden('classification_id')); ?>

                        <div class="form-group">
                            <?php echo e(html()->label(trans('Trainer'))->class('control-label label_required')); ?>

                            <?php echo e(html()->select('trainer_id')->class('users-search form-control ')->id('trainersSelect')->data('placeholder', trans('Select'))->options([null => ''])->options(\Tasawk\Employees\Models\Trainer::listsTranslations("name")->pluck("name","id"))); ?>

                            <p class="text-danger"></p>
                        </div>
                        <div class="form-group">
                            <?php echo e(html()->label(trans('Date'))->class('control-label label_required')); ?>

                            <?php echo e(html()->date('date')->class(' form-control')); ?>

                            <p class="text-danger"></p>
                        </div>
                        <div class="form-group">
                            <?php echo e(html()->label(trans('Period'))->class('control-label label_required')); ?>

                            <?php echo e(html()->hidden('period')); ?>

                            <div class="periods d-flex justify-content-between">
                            <p class="font-weight-bolder ">    <?php echo app('translator')->get("Select date and trainer to show periods"); ?></p>
                            </div>

                            <p class="general-error text-danger"></p>
                        </div>

                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary"
                                    data-dismiss="modal"><?php echo app('translator')->get('Cancel'); ?></button>
                            <button type="button" class="btn btn-primary"
                                    id="reserveSubmitFrom"><?php echo app('translator')->get('Save'); ?></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopPush(); ?>


<?php $__env->startPush('scripts'); ?>
    <script>

        $(".reservation-pop-up-btn").on("click", function (e) {
            e.preventDefault();
            const reserveFrom = $("#reserveFrom");
            let id = $(this).data('id');
            let trainer_id = $(this).data('trainer-id');
            let classification_id = $(this).data('classification-id');
            let modalContainer = $('#reserveModal');
            reserveFrom.find("input[name='subscription_id']").val(id);
            reserveFrom.find("input[name='classification_id']").val(classification_id);

            modalContainer.modal('show');
            $.ajax({
                url: route("admin.subscriptions.get-trainers", {classification_id}),
                success: function (data) {
                    $(".users-search").html(data);
                    $("select[name='trainer_id']").val(trainer_id);
                }
            })

        });
        const fetchPeriods = (subscription_id, trainer_id, classification_id, date) => {
            $.ajax({
                url: route("admin.subscriptions.get-trainer-times"),
                data: {
                    id: subscription_id,
                    trainer_id: trainer_id,
                    classification_id: classification_id,
                    date: date

                },
                success: function (data) {
                    $(".periods").html(data);
                }
            })
        }
        $("select[name='trainer_id']").on("change", function (e) {
            e.preventDefault();
            const trainer_id = $(this).val();
            const classification_id = $("input[name='classification_id']").val();
            const subscription_id = $("input[name='subscription_id']").val()
            fetchPeriods(subscription_id, trainer_id, classification_id, $("input[name='date']").val())

        });
        $("input[name='date']").on("change", function (e) {
            const trainer_id = $("select[name='trainer_id']").select2("val");
            console.log(trainer_id);
            const classification_id = $("input[name='classification_id']").val();
            const subscription_id = $("input[name='subscription_id']").val()
            fetchPeriods(subscription_id, trainer_id, classification_id, $(this).val())

        });

        $(document).on("click", "#reserveSubmitFrom", () => {
            let data = $("#assignUsersFrom").serialize();
            let couponID = $('#assignUsersSubmitFrom').data('trainer-id');
            $.ajax({
                url: route("admin.subscriptions.reserve", [$("input[name='subscription_id']").val(), $("#trainersSelect").val()]),
                type: "POST",
                data: {
                    date: $("input[name='date']").val(),
                    period: $("input[name='period']:checked").val()
                },
                success: function (data) {
                    notify("<?php echo app('translator')->get('Success'); ?>", "<?php echo app('translator')->get('Done successfully'); ?>");
                    setTimeout(() => {
                        location.reload()
                    }, 2000);
                },
                error: (data) => {
                    const errors = data.responseJSON.errors;
                    if (!Object.entries(errors).length) {
                        $(".general-error").html(data.responseJSON.message);
                    }
                    for (const [field, errs] of Object.entries(errors)) {
                        $(`[name='${field}']`).parents(".form-group").find("p.text-danger").html(errs[0]);
                    }
                }
            })
        });
        $(".users-search").select2({
            allowClear: true
        });
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\Workstation\Taswk\medhal\Packages\subscriptions\src/../resources/views/themes/vuexy/Dashboard/modals/reserve_modal.blade.php ENDPATH**/ ?>