<?php $attributes = $attributes->exceptProps([
'route' => '',
'id' => '',
'title'=>'Show'

]); ?>
<?php foreach (array_filter(([
'route' => '',
'id' => '',
'title'=>'Show'

]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<a class="btn btn-icon btn-sm btn-outline-primary waves-effect mr-1" href="<?php echo e(route($route,$id)); ?>">
    <i data-feather='eye'></i>

</a>
<?php /**PATH D:\Workstation\Taswk\medhal\Packages\admin-theme-vuexy\src/views/components/list-item-show.blade.php ENDPATH**/ ?>