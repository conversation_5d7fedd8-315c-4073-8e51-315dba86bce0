<div id="session-ends-overlay" style=" width: 100%; height: 100%; background: #949494; position: fixed; top: 0; left: 100000px; z-index: 1500; opacity: 0.4; ">
    <p style=" font-size: 60px; color: black; top: 50%; position: relative; text-align: center; user-select: none; ">Your session will ends in <span id="session-ends-in"></span></p>
</div>
<?php $__env->startPush('scripts'); ?>
    <script>
        ;(function () {
            return;
            let timer, currSeconds = 0;
            let maxSessionTime = 60;
            let sessionTimeGraceful = 10;

            function resetTimer() {
                clearInterval(timer);
                currSeconds = 0;
                sessionTimeGraceful = 10;
                timer = setInterval(startIdleTimer, 1000);
            }

            window.onload = resetTimer;
            window.onmousemove = resetTimer;
            window.onmousedown = resetTimer;
            window.ontouchstart = resetTimer;
            window.onclick = resetTimer;
            window.onkeypress = resetTimer;
            let hideOverlay = function () {
                document.getElementById('session-ends-overlay').style.left = '100000px'
                resetTimer();
            };
            let sessionEndsOverlay = document.getElementById('session-ends-overlay');
            sessionEndsOverlay.addEventListener('click', hideOverlay)
            sessionEndsOverlay.addEventListener('mousemove', hideOverlay)
            sessionEndsOverlay.addEventListener('mousedown', hideOverlay)
            sessionEndsOverlay.addEventListener('touchstart', hideOverlay)
            sessionEndsOverlay.addEventListener('keypress', hideOverlay)

            function startIdleTimer() {
                currSeconds++;
                if (currSeconds >= maxSessionTime) {
                    document.getElementById('session-ends-in').innerText = --sessionTimeGraceful;
                    sessionEndsOverlay.style.left = '0'
                    if (sessionTimeGraceful <= 0) {
                        window.location.assign('<?php echo e(route('logout')); ?>');
                    }
                } else {
                    sessionEndsOverlay.style.left = '100000px'
                }
            }
        })();
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\Workstation\Taswk\medhal\Packages\admin-theme-vuexy\src/views/parts/footer/session-ends-overlay.blade.php ENDPATH**/ ?>