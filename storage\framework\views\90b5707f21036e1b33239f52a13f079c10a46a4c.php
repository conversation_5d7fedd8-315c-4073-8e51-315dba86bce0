<?php $__env->startPush('modals'); ?>
    <!-- Modal -->
    <div class="modal fade" id="assignUsersModal" tabindex="-1" aria-labelledby="classificationModalLabel"
         aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="classificationModalLabel"><?php echo e(__('Change Trainer')); ?></h5>

                </div>
                <div class="modal-body">
                    <form id="assignUsersFrom" method="post">
                        <?php echo csrf_field(); ?>
                        <div class="form-group">
                            <?php echo e(html()->label(trans('Trainer'))->class('control-label label_required')); ?>

                            <?php echo e(html()->select('trainer_id')->class('users-search form-control')->data('placeholder', trans('Select'))->options([null => ''])->options([])); ?>

                            <p class="text-danger"></p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary"
                                    data-dismiss="modal"><?php echo app('translator')->get('Cancel'); ?></button>
                            <button type="button" class="btn btn-primary"
                                    id="assignUsersSubmitFrom"><?php echo app('translator')->get('Save'); ?></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopPush(); ?>


<?php $__env->startPush('scripts'); ?>
    <script>

        $(".assign-users-btn").on("click", function (e) {
            e.preventDefault();
            let id = $(this).data('id');
           let classification_id = $(this).data('classification-id');

            let modalContainer = $('#assignUsersModal');
            modalContainer.modal('show');
            $("#assignUsersSubmitFrom").data('trainer-id', id);
            $.ajax({
                url: route("admin.subscriptions.get-trainers",{classification_id}),
                success: function (data) {
                    console.log(data);
                    $(".users-search").html(data)
                }
            })

        });

        $(document).on("click", "#assignUsersSubmitFrom", () => {
            let data = $("#assignUsersFrom").serialize();
            let couponID = $('#assignUsersSubmitFrom').data('trainer-id');
            $.ajax({
                url: route("admin.subscriptions.change-trainer", couponID),
                type: "POST",
                data,
                success: function (data) {
                    notify("<?php echo app('translator')->get('Success'); ?>", "<?php echo app('translator')->get('Done successfully'); ?>");
                    setTimeout(() => {
                        location.reload()
                    }, 2000);
                },
                error: (data) => {
                    const errors = data.responseJSON.errors;
                    console.log(errors);
                    for (const [field, errs] of Object.entries(errors)) {
                        console.log(errs[0]);
                        $(`[name='${field}']`).parents(".form-group").find("p.text-danger").html(errs[
                            0]);
                    }
                }
            })
        });
        $(".users-search").select2({
            allowClear: true
        });
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\Workstation\Taswk\medhal\Packages\subscriptions\src/../resources/views/themes/vuexy/Dashboard/modals/assign_trainer_modal.blade.php ENDPATH**/ ?>