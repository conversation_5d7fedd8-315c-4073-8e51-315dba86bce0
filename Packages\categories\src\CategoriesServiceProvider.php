<?php

namespace Tasawk\Categories;

use AdminBase;
use Dingo\Api\Contract\Http\Request;
use QCod\AppSettings\Setting\AppSettings;
use Tasawk\AdminPanel\BaseServiceProvider;
use Tasawk\AdminPanel\Lib\Breadcrumb\Item;
use Tasawk\Categories\Model\Categories;
use Tasawk\Categories\Resources\CategoryResource;
use TorMorten\Eventy\Facades\Eventy;

class CategoriesServiceProvider extends BaseServiceProvider {
    public function boot() {
        $this->loadViews(__DIR__ . '/../resources/views', 'category');
        $this->hasMigration();
        $this->route(__DIR__ . '/../routes/web.php');
        $this->route(__DIR__ . '/../routes/api.php');
        $this->hasLanguage();
        // Eventy::addFilter('admin.sidebar.menu.group.administration', function ($menu) {
        //     $menu['categories'] = [
        //         'order'=> 7,
        //         'title' => __("Categories"),
        //         'icon' => 'icon-filter4',
        //         'path_type' => 'route',
        //         'path' => 'admin.categories.index',
        //         'permissions'=>'categories.index'
        //     ];
        //     return $menu;
        // }, 1);
        include_once __DIR__ .'/../routes/breadcrumbs.php';
    }
    public function register() {
     $this->mergeConfigFrom(__DIR__."./../config/category.php",'category');
    }
}
