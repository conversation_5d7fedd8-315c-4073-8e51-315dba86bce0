<?php $attributes = $attributes->exceptProps([
    'form-class' => '',
    'panel-id' => '',
    'title' => 'Save',
    'class' => '',
    'callback' => null,
    'disableRedirect' => false,
]); ?>
<?php foreach (array_filter(([
    'form-class' => '',
    'panel-id' => '',
    'title' => 'Save',
    'class' => '',
    'callback' => null,
    'disableRedirect' => false,
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<button type="submit" data-has-ajax <?php if($callback): ?> data-callback="<?php echo e($callback); ?>" <?php endif; ?>

<?php if($disableRedirect): ?> data-disable-redirect="<?php echo e($disableRedirect ? 'true' : 'false'); ?>" <?php endif; ?>
    data-ajax-form="<?php echo e($formClass); ?>" data-ajax-ui-block='[data-panel-id="<?php echo e($panelId); ?>"]'
    class="btn btn-primary pull-right <?php echo e($class); ?>">
    <i data-feather='save'></i>
    <?php echo app('translator')->get($title); ?>
</button>
<?php /**PATH D:\Workstation\Taswk\medhal\Packages\admin-theme-vuexy\src/views/components/ajax-submit-btn.blade.php ENDPATH**/ ?>