    <div id="translation" class="modal fade out">
        <div class="modal-dialog">
            <div class="modal-content modal-dialog-scrollable">
                <div class="modal-header">
                    <div class="pull-right">
                        <button type="submit" class="btn btn-primary" form="translation-form" title="<?php echo e(__('Save')); ?>">
                            <i data-feather="database" stroke-width="7"></i></button>
                        <button type="button" class="btn btn-danger " data-dismiss="modal" title="<?php echo e(__('Close')); ?>">
                            <i data-feather="x" stroke-width="7"></i></button>
                    </div>
                    <h5 class="modal-title"><?php echo app('translator')->get("Translation"); ?></h5>
                </div>
                <div class="modal-body">
                    <form action="<?php echo e(route("admin.translate.page")); ?>" method="get"
                        id="translation-form">
                        <div class="col-md-12">
                            <input class="form-control translation-search" type="text"
                                placeholder="<?php echo e(__("Write To Search !")); ?>"
                                style=" width: 100%; margin-bottom: 15px; ">
                        </div>
                        <?php $__currentLoopData = trans()->notTranslated; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $word): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="translation-row row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <input class="form-control translation-val" type="text" value="<?php echo e($word); ?>">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <input class="form-control" type="text" name="keys[<?php echo e($word); ?>]">
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </form>

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger" data-dismiss="modal"><?php echo app('translator')->get("Close"); ?></button>
                    <button type="submit" form="translation-form" class="btn btn-primary"><?php echo app('translator')->get("Save Changes"); ?></button>
                </div>
            </div>
        </div>
    </div>
    <script>
        $('.translation-search').keyup(function () {
            let inputValue = $(this).val().toLowerCase();
            console.log(inputValue);
            $('.translation-val').each(function (i, el) {
                let _el = $(el);
                let text = _el.val().toLowerCase();
                if (text.trim() === "") {
                    _el.parents(".translation-row").show();
                    return;
                }
                if (text.search(inputValue) === -1) {
                    _el.parents(".translation-row").hide();
                } else {
                    _el.parents(".translation-row").show();
                }
            });
        });

    </script>
<?php /**PATH D:\Workstation\Taswk\medhal\Packages\utilities\src/../resources/views/themes/vuexy/translation/common/modal.blade.php ENDPATH**/ ?>