<?php $__env->startPush('modals'); ?>
    <!-- Modal -->
    <div class="modal fade" id="paymentModal" tabindex="-1" aria-labelledby="paymentModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="paymentModalLabel"><?php echo app('translator')->get('Confirm payment'); ?></h5>

                </div>
                <div class="modal-body">
                    <form id="paymentForm" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>

                        <div class="form-group">
                            <div class="label-control"><?php echo e(__('Payment method')); ?></div>
                            <select class="select-search2  form-control payment-search"
                                    data-placeholder="<?php echo app('translator')->get('Choose'); ?>"
                                    name="collection_method"
                                    id="payment_method"
                            >
                                <option value=""></option>
                                <option value="cash"><?php echo e(__('Cash')); ?></option>
                                <option value="center_device"><?php echo e(__('Center device')); ?></option>
                            </select>
                            <p class="text-danger collection_method"></p>
                        </div>

                        <div class="form-group">
                            <label class="control-label"><?php echo e(__('Payment image')); ?></label>

                            <input type="hidden" name="payment_image">
                            <input type="file" class="file " name="payment_image"
                                   placeholder="<?php echo e(__('Image')); ?>">
                            <p class="text-danger payment_image"></p>
                        </div>

                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary"
                                    data-dismiss="modal"><?php echo app('translator')->get('Cancel'); ?></button>
                            <button type="button" class="btn btn-primary btn_payment"
                                    data-subscription-id="{ID}"><?php echo app('translator')->get('Save'); ?></button>
                        </div>

                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopPush(); ?>


<?php $__env->startPush('scripts'); ?>
    <script>
        $(".payment-pop-up-btn").on("click", function () {
            let {
                id,
                payment_method
            } = $(this).data('subscription');
            let modalContainer = $('#paymentModal');
            modalContainer.modal('show');
            modalContainer.html(modalContainer.html().replaceAll("{ID}", id).replace("{payment_method}",
                payment_method));
            $(".select-search2").select2({
                allowClear: true
            });
            $(".file").dropify();

        });
        $(document).ready(function () {
            $('payment_method').each(function () {
                $(this).val($(this).val().trim());
            });
            $(document).on("click", ".btn_payment", () => {
                let data = new FormData(document.getElementById('paymentForm'));

                let subscriptionID = $('.btn_payment').data('subscription-id');

                $.ajax({
                    url: route("admin.subscriptions.confirm-payment", subscriptionID),
                    type: "POST",
                    data: data,
                    processData: false,
                    contentType: false,
                    success: function (data) {
                        notify("<?php echo app('translator')->get('Success'); ?>", "<?php echo app('translator')->get('Subscription confirm payment successfully'); ?>");
                        setTimeout(() => {
                            location.reload()
                        }, 2000);
                    },
                    error: (data) => {
                        const errors = data.responseJSON.errors;
                        for (const [field, errs] of Object.entries(errors)) {

                            $(`.${field} `).html(errs[0]);
                        }
                    }
                })
            });
        });
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\Workstation\Taswk\medhal\Packages\subscriptions\src/../resources/views/themes/vuexy/Dashboard/modals/confirm_payment_modal.blade.php ENDPATH**/ ?>