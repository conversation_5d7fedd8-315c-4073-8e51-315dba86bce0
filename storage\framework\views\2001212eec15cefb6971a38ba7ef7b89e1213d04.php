<div class="card">
    <div class="card-header">
        <h4 class="card-title"><?php echo app('translator')->get('Sessions data'); ?></h4>
        <div class="heading-elements">
            <ul class="list-inline mb-0">
                <li>
                    <a data-action="collapse">
                        <i data-feather="chevron-down"></i>
                    </a>
                </li>
            </ul>
        </div>
    </div>
    <div class="card-content collapse show">
        <div class="card-body table-responsive">
            <table class="table datatable-basic">
                <thead>
                    <tr>
                        <th ><?php echo app('translator')->get("ID"); ?></th>
                        <th><?php echo app('translator')->get('Code'); ?></th>
                        <th><?php echo app('translator')->get('Session type'); ?></th>
                        <th><?php echo app('translator')->get('Status'); ?></th>
                        <th><?php echo app('translator')->get('Date'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $subscription->reservations()->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $session): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>

                            <td><?php echo e($session->id ?? ''); ?></td>
                            <td><?php echo e($session->reservation->code ?? ''); ?></td>
                            <td><?php echo app('translator')->get(Str::headline(Str::lower($session->type)) ); ?></td>
                            <td><?php echo app('translator')->get(Str::headline($session->status)); ?> </td>
                            <td><?php echo e($session->reservation->date ?? ''); ?></td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="2" class="text-center"><?php echo __('No Data Found'); ?></td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php /**PATH D:\Workstation\Taswk\medhal\Packages\subscriptions\src/../resources/views/themes/vuexy/components/reservation_data.blade.php ENDPATH**/ ?>