<?php
if (!function_exists('tenant') && !class_exists(\Tasawk\Support\Models\ChangeLog::class)) {
    return;
}
$nav_log = [];//(new \Tasawk\Support\Models\ChangeLog)->notification();
?>
<?php if( false &&!empty($nav_log) ): ?>
    <li class="nav-item dropdown dropdown-notification mr-25">
        <a class="nav-link"
           title="<?php echo e(__('Change logs')); ?>"
           href="<?php echo e(route('admin.change-log.show',$nav_log['id'])); ?>">
            <i class="icon-umbrella"></i>
            <span class="badge badge-pill badge-danger badge-up">1</span>
        </a>
    </li>
<?php endif; ?>
<?php /**PATH D:\Workstation\Taswk\medhal\Packages\utilities\src/../resources/views/themes/vuexy/change_log/common/index.blade.php ENDPATH**/ ?>