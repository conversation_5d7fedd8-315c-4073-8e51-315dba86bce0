<?php

namespace Tasawk\Subscriptions\Filters\Subscriptions;

use Illuminate\Database\Eloquent\Builder;
use Tasawk\AdminPanel\Interfaces\IFilter;
use Tasawk\AdminPanel\Lib\Filters\Html\Container;

class SubscriptionsidFilter implements IFilter
{
    use Container;

    public function filter(Builder $builder, $value)
    {
          return $builder->where('id', $value);
    }

    public function toHtml()
    {

        $label = __('Subscription number');
        $html = html()->text('subscription_id')
            ->value(request('subscription_id'))
            ->placeholder($label)
            ->class('form-control');
        return $this->htmlContainer($html, $label);
    }
}
