<?php $__env->startPush('modals'); ?>
    <!-- Modal -->
    <div class="modal fade" id="newCustomerModal" tabindex="-1" aria-labelledby="CustomerModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="CustomerModalLabel"><?php echo e(__('Create new customer')); ?></h5>

                </div>
                <div class="modal-body">
                    <form action="<?php echo e(route('admin.subscriptions.storeCustomer')); ?>" method="post"
                        class="form-horizontal crm-create-customer-form" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <div class="form-group">
                            <div class="col-lg-12">
                                <div class="card" data-panel-id="crm-create-customer">
                                    <div class="card-header">
                                        <h6 class="card-title"><?php echo app('translator')->get('Basic data'); ?> </h6>
                                        <div class="heading-elements">
                                            <ul class="list-inline mb-0">
                                                <li>
                                                    <a data-action="collapse"
                                                        data-user-setting='{"admin.crm.customer.basic-data-panel":"<?php echo e(setting_user()->get('admin.crm.customer.basic-data-panel') == 'true' ? 'false' : 'true'); ?>"}'>
                                                        <?php if(setting_user()->get('admin.crm.customer.basic-data-panel') == 'true'): ?>
                                                            <i data-feather="chevron-down"></i>
                                                        <?php else: ?>
                                                            <i data-feather="chevron-up"></i>
                                                        <?php endif; ?>
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div
                                        class="card-content collapse <?php echo e(setting_user()->get('admin.crm.customer.basic-data-panel') == 'true' ? 'show' : ''); ?>">

                                        <div class="card-body">
                                            <div class="form-group">
                                                <label class="control-label label_required"><?php echo app('translator')->get('Name'); ?> :</label>
                                                <input type="text" class="form-control" value="<?php echo e(old('name')); ?>"
                                                    name="name" placeholder="<?php echo e(__('Name')); ?>">
                                                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <span class="text-danger"><?php echo e($message); ?></span>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </div>
                                            <div class="form-group">
                                                <label class="control-label"><?php echo app('translator')->get('Email'); ?> :</label>
                                                <input type="email" class="form-control" value="<?php echo e(old('email')); ?>"
                                                    name="email" placeholder="<?php echo e(__('Email')); ?>"
                                                    autocomplete="new-username">
                                                <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <span class="text-danger"><?php echo e($message); ?></span>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </div>
                                            
                                            <div class="form-group">
                                                <label class="control-label label_required"><?php echo app('translator')->get('Phone'); ?> :</label>
                                                <input type="text" class="form-control" value="<?php echo e(old('phone')); ?>"
                                                    name="phone" placeholder="5********">
                                                <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <span class="text-danger"><?php echo e($message); ?></span>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </div>

                                            <div class="form-group">
                                                <label class="control-label label_required"><?php echo app('translator')->get('Password'); ?> :</label>
                                                <input type="password" class="form-control" name="password"
                                                    autocomplete="new-password" placeholder="<?php echo e(__('Password')); ?>">
                                                <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <span class="text-danger"><?php echo e($message); ?></span>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </div>
                                            <div class="form-group">
                                                <label class="control-label label_required"><?php echo app('translator')->get('Password Confirmation'); ?> :</label>
                                                <input type="password" class="form-control" name="password_confirmation"
                                                    autocomplete="new-password"
                                                    placeholder="<?php echo e(__('Password Confirmation')); ?>">
                                                <?php $__errorArgs = ['password_confirmation'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <span class="text-danger"><?php echo e($message); ?></span>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </div>
                                            <div class="form-group">
                                                <?php echo e(html()->label(trans('Zone'))->class('control-label label_required')); ?>

                                                <?php echo e(html()->select('zone_id')->class('select-search form-control zone-select')->data('placeholder', trans('Select'))->options([null => ''])->options($zones)); ?>

                                            </div>
                                            <div class="form-group">
                                                <?php echo e(html()->label(trans('City'))->class('control-label label_required')); ?>

                                                <?php echo e(html()->select('city_id')->class('select-search form-control city-select')->data('placeholder', trans('Select'))->options([null => ''])->options([])); ?>

                                            </div>
                                            <div class="form-group">
                                                <label><?php echo app('translator')->get('Has previous experience'); ?></label>
                                                <div class="mt-50">
                                                    <div class="custom-control custom-switch custom-control-inline">
                                                        <input type="hidden" name="has_previous_experience"
                                                            value="0" />
                                                        <input id="has_previous_experience" type="checkbox"
                                                            class="custom-control-input" name="has_previous_experience"
                                                            value="0" />
                                                        <label class="custom-control-label"
                                                            for="has_previous_experience"></label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label><?php echo app('translator')->get('Status'); ?></label>
                                                <div class="mt-50">
                                                    <div class="custom-control custom-switch custom-control-inline">
                                                        <input id="status" type="checkbox" class="custom-control-input"
                                                            name="status" value="1" />
                                                        <label class="custom-control-label" for="status"></label>
                                                    </div>
                                                </div>
                                            </div>


                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo app('translator')->get('Cancel'); ?></button>
                            
                            <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = $__env->getContainer()->make(Illuminate\View\AnonymousComponent::class, ['view' => 'vuexy::components.ajax-submit-btn','data' => ['formClass' => '.crm-create-customer-form','panelId' => 'crm-create-customer','title' => 'Create','disableRedirect' => 'true']]); ?>
<?php $component->withName('vuexy::ajax-submit-btn'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes(['form-class' => '.crm-create-customer-form','panel-id' => 'crm-create-customer','title' => 'Create','disableRedirect' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopPush(); ?>


<?php $__env->startPush('scripts'); ?>
    <script>
        $(".new-customer-btn").on("click", function(e) {
            e.preventDefault();

            let modalContainer = $('#newCustomerModal');
            modalContainer.modal('show');
            $("#newCustomerSubmitFrom").data();
            $.ajax({
                url: route("admin.subscriptions.storeCustomer"),

            })

        });
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\Workstation\Taswk\medhal\Packages\subscriptions\src/../resources/views/themes/vuexy/Dashboard/modals/new_customer_modal.blade.php ENDPATH**/ ?>