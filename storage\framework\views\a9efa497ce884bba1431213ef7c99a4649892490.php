<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check("notifications.index")): ?>
    <?php
        $unreadNotificationsCount = auth()->user()->unreadNotifications->count();
    ?>
    <li class="nav-item dropdown dropdown-notification mr-25">
        <a class="nav-link" href="javascript:void(0);" data-toggle="dropdown">
            <i class="ficon" data-feather="bell"></i>
            <span class="badge badge-pill badge-danger badge-up" id="notification-counter"><?php echo e($unreadNotificationsCount); ?></span>
        </a>
        <ul class="dropdown-menu dropdown-menu-media dropdown-menu-right" style="z-index: 20">
            <li class="dropdown-menu-header">
                <div class="dropdown-header d-flex">
                    <h4 class="notification-title mb-0 mr-auto"><?php echo app('translator')->get('Notifications'); ?></h4>
                    <div class="badge badge-pill badge-light-primary"><span id="notification-list-counter"><?php echo e($unreadNotificationsCount); ?></span> <?php echo app('translator')->get('New'); ?></div>
                </div>
            </li>
            <li class="scrollable-container media-list" id="notifications-list">
                <?php echo $__env->make('notification::parts.notifications-list',['unreadNotificationsList'=>   auth()->user()->unreadNotifications->take(5)], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            </li>
            <li class="dropdown-menu-footer">
                <a class="btn btn-primary btn-block"
                   href="<?php echo e(route('admin.notifications.index')); ?>"><?php echo app('translator')->get('Read all notifications'); ?></a>
            </li>
        </ul>
    </li>
    <?php $__env->startPush('scripts'); ?>
        <script>
            $('.notification-item').on("click", function (e) {
                let data = new FormData();
                data.append('notification_id', $(this).data('notification-item-id'));
                data.append('_token', _token);
                navigator.sendBeacon(route('admin.notifications.mark-as-read'), data);
            })
        </script>
    <?php $__env->stopPush(); ?>
<?php endif; ?>
<?php /**PATH D:\Workstation\Taswk\medhal\Packages\notifcation\app/../resources/views/themes/vuexy/parts/top-bar-icon.blade.php ENDPATH**/ ?>