
<?php $__env->startSection('head_title', __('Subscriptions')); ?>
<?php $__env->startSection('title', __('Subscriptions Show')); ?>
<?php $__env->startSection('content'); ?>
    <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = $__env->getContainer()->make(Illuminate\View\AnonymousComponent::class, ['view' => 'vuexy::components.filter','data' => ['id' => 'subscription-index-filter','filters' => (new Tasawk\Subscriptions\Filters\SubscriptionFilter(request()))->getFilters(false)]]); ?>
<?php $component->withName('vuexy::filter'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes(['id' => 'subscription-index-filter','filters' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute((new Tasawk\Subscriptions\Filters\SubscriptionFilter(request()))->getFilters(false))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
    <div class="card <?php echo e(get_user_panel_collapse_state_class('subscriptions-data')); ?>" data-panel-id="subscriptions-data">
        <div class="card-header">
            <h5 class="card-title"><?php echo app('translator')->get('Subscriptions Data'); ?></h5>
        </div>
        <div class="card-body table-responsive">
            <table class="table datatable-basic">
                <thead>
                    <tr>
                        <th <?php echo \limitless_table_head_width(1); ?>>#</th>
                        <th><?php echo app('translator')->get('Subscription number'); ?></th>
                        <th><?php echo app('translator')->get('User name'); ?></th>
                        <th><?php echo app('translator')->get('User phone'); ?></th>
                        <th><?php echo app('translator')->get('Status'); ?></th>
                        <th><?php echo app('translator')->get('Classification'); ?></th>
                        <th><?php echo app('translator')->get('Plan'); ?></th>
                        <th><?php echo app('translator')->get('Trainer'); ?></th>
                        <th><?php echo app('translator')->get('Total'); ?></th>
                        
                        
                        
                        <th><?php echo app('translator')->get('Created at'); ?></th>
                        <th><?php echo app('translator')->get('Expires at'); ?></th>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['subscriptions.update', 'subscriptions.delete', 'subscriptions.show'])): ?>
                            <th><?php echo app('translator')->get('Actions'); ?></th>
                        <?php endif; ?>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $lists; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $one): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        
                        <tr>
                            <td><?php echo e($loop->index + 1); ?></td>
                            <td><?php echo e($one->id); ?></td>
                            <td><?php echo e($one->user->name ?? ''); ?></td>
                            <td><?php echo e($one->user->phone ?? ''); ?></td>
                            <td>
                                <?php echo e(__(Str::headline($one->status)) ?? ''); ?>

                            </td>
                            <td><?php echo e($one->classification->title ?? __('Not selected yet')); ?></td>
                            <td><?php echo e($one->plan->name ?? ''); ?></td>
                            <td><?php echo e($one->trainer->name ?? __('Not selected yet')); ?></td>
                            <td>
                                <?php if($one->type == 'in_center'): ?>
                                    <?php echo e($one->cost_after_taxes); ?>

                                <?php else: ?>
                                    <?php echo e($one->total); ?>

                                <?php endif; ?>
                            </td>
                            
                            
                            
                            <?php if($one->type == 'in_center'): ?>
                                <td><?php echo e($one->start_date ?? '---------'); ?></td>
                                <td><?php echo e($one->end_date ?? '---------'); ?></td>
                            <?php else: ?>
                                <td><?php echo e($one->created_at ?? '---------'); ?></td>
                                <td><?php echo e($one->expires_at ?? '---------'); ?></td>
                            <?php endif; ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['subscriptions.update', 'subscriptions.delete', 'subscriptions.show'])): ?>
                                <td>
                                    <div class="d-flex justify-content-between">


                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('subscriptions.show')): ?>
                                            <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = $__env->getContainer()->make(Illuminate\View\AnonymousComponent::class, ['view' => 'vuexy::components.list-item-show','data' => ['route' => 'admin.subscription.show','id' => $one->id]]); ?>
<?php $component->withName('vuexy::list-item-show'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes(['route' => 'admin.subscription.show','id' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($one->id)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
                                        <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('subscriptions.update')): ?>
                                                <?php if (isset($component)) { $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4 = $component; } ?>
<?php $component = $__env->getContainer()->make(Illuminate\View\AnonymousComponent::class, ['view' => 'vuexy::components.list-item-edit','data' => ['route' => 'admin.subscription.edit','id' => $one->id]]); ?>
<?php $component->withName('vuexy::list-item-edit'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php $component->withAttributes(['route' => 'admin.subscription.edit','id' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($one->id)]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4)): ?>
<?php $component = $__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4; ?>
<?php unset($__componentOriginalc254754b9d5db91d5165876f9d051922ca0066f4); ?>
<?php endif; ?>
                                            <?php endif; ?>
                                            <button
                                                class="btn btn-icon btn-sm btn-outline-secondary waves-effect mr-1 reservation-pop-up-btn"
                                                title="<?php echo e(__('Reserve')); ?>"
                                                data-id='<?php echo e($one->id); ?>'
                                                data-trainer-id='<?php echo e($one->trainer_id); ?>'
                                                data-classification-id="<?php echo e($one->classification_id); ?>"
                                            >

                                                <i data-feather='clock'></i>
                                            </button>

                                            <button
                                                class="btn btn-icon btn-sm btn-outline-info waves-effect mr-1 classification-pop-up-btn"
                                                title="<?php echo e(__('Classification')); ?>" data-order='{"id":<?php echo e($one->id); ?> }'>
                                                <i data-feather='send'></i>
                                            </button>

                                        <?php if($one->payment_status != 'paid' && $one->payment_data == 'in_center' && $one->type == 'online'): ?>
                                            <button
                                                class="btn btn-icon btn-sm btn-outline-success waves-effect mr-1 payment-pop-up-btn"
                                                title="<?php echo e(__('Confirm payment')); ?>"
                                                data-subscription='{"id":<?php echo e($one->id); ?> }'>
                                                <i data-feather='check'></i>
                                            </button>
                                        <?php endif; ?>
                                        <?php if(auth()->user()->can('subscriptions.update') &&
                                                $one->active &&
                                                $one->plan->trainers()->where('trainer_id', $one->trainer_id)
                                                    ?->first()?->pivot?->price <= 0): ?>
                                            <a href=""
                                                class="btn btn-icon btn-sm btn-outline-primary waves-effect mr-1 assign-users-btn"
                                                data-id="<?php echo e($one->id); ?>"
                                                data-classification-id="<?php echo e($one->classification_id); ?>">
                                                <i data-feather='refresh-ccw'></i>
                                            </a>
                                        <?php endif; ?>

                                        
                                        
                                        
                                        

                                    </div>
                                </td>
                            <?php endif; ?>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="2" class="text-center"><?php echo __('No Data Found'); ?></td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
            
            <?php if(request('limit') != 'all'): ?>
                <div class="mt-1">
                    <?php echo $lists->appends(request()->except(['export']))->links(); ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('subscriptions::Dashboard.modals.classification_modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make('subscriptions::Dashboard.modals.assign_trainer_modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make('subscriptions::Dashboard.modals.reserve_modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make('subscriptions::Dashboard.modals.confirm_payment_modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php echo $__env->make('admin::layouts.2_col', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Workstation\Taswk\medhal\Packages\subscriptions\src/../resources/views/themes/vuexy/Dashboard/index.blade.php ENDPATH**/ ?>