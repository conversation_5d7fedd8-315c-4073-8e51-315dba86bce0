<script>
       $("input[name='sidebar-search']").keyup(function() {

           var inputValue = $(this).val().toLowerCase();
           $('.navigation  li').not('.navigation-header').each(function(i, el) {
               var _el = $(el);
               var text = _el.text().toLowerCase();
               if (text.trim() === "") {
                   _el.show();
                   return;
               }
               if (text.search(inputValue) === -1) {
                     _el.hide();
                   }
                   else {
                    _el.show();
                    // _el.find("ul > li").addClass('show');
                   }
           });
       });
   </script>
<?php /**PATH D:\Workstation\Taswk\medhal\Packages\utilities\src/../resources/views/themes/vuexy/search/sidebar/scripts.blade.php ENDPATH**/ ?>