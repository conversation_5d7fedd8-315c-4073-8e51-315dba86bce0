<?php
$specializations = \Tasawk\Employees\Models\Specialization::where('status', 1)
    ->listsTranslations('title')
    ->pluck('title', 'id');
?>
<?php $__env->startPush('modals'); ?>
    <!-- Modal -->
    <div class="modal fade" id="classificationModal" tabindex="-1" aria-labelledby="classificationModalLabel"
        aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="classificationModalLabel"><?php echo e(__('Select classification')); ?></h5>

                </div>
                <div class="modal-body">
                    <form id="classificationForm">

                        <div class="form-group">
                            <?php echo e(html()->label(trans('Classification'))->class('control-label label_required')); ?>

                            <?php echo e(html()->select('classification_id')->class('select-search form-control specialization-select')->id('classificationSelect')->data('placeholder', trans('Select'))->options([null => ''])->options($specializations)); ?>

                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo app('translator')->get('Cancel'); ?></button>
                            <button type="button" class="btn btn-primary btn_classification"
                                data-order-id="{ID}"><?php echo app('translator')->get('Save'); ?></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopPush(); ?>


<?php $__env->startPush('scripts'); ?>
    <script>
        $(function() {
            $(document).on("change", "#classificationSelect", function() {
                let id = $(".btn_classification").data('order-id');
                console.log(id);
                let classification = $(this).val();
                $.ajax({
                    url: route("admin.subscriptions.classification", [id, classification]),
                    success: function(data) {
                        select.html(data).removeAttr("disabled");
                    }
                });
            });
        });

        $(".classification-pop-up-btn").on("click", function() {
            let {
                id
            } = $(this).data('order');
            let modalContainer = $('#classificationModal');
            modalContainer.modal('show');
            $('.btn_classification').data('order-id', id);
            $(".select2-search").select2({
                allowClear: true
            });

        });
        $(document).on("click", ".btn_classification", () => {
            let data = $("#classificationForm").serialize();
            let orderID = $('.btn_classification').data('order-id');
            $.ajax({
                url: route("admin.subscriptions.classification", orderID),
                type: "POST",
                data,
                success: function(data) {
                    notify("<?php echo app('translator')->get('Success'); ?>", "<?php echo app('translator')->get('Add classification to customer successfully'); ?>");
                    location.reload();
                },
                error: (data) => {
                    const errors = data.responseJSON.errors;
                    console.log(errors);
                    for (const [field, errs] of Object.entries(errors)) {
                        console.log(errs[0]);
                        $(`[name='${field}']`).parents(".form-group").find("p.text-danger").html(errs[
                            0]);
                    }
                }
            })
        });
    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\Workstation\Taswk\medhal\Packages\subscriptions\src/../resources/views/themes/vuexy/Dashboard/modals/classification_modal.blade.php ENDPATH**/ ?>