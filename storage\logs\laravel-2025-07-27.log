[2025-07-27 13:45:13] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(153): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(137): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#10 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#11 D:\\Workstation\\Taswk\\medhal\\public\\index.php(49): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#12 {main}
"} 
[2025-07-27 13:47:22] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 13:47:22] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 13:47:22] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 13:47:22] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 13:47:22] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 13:47:22] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 13:48:28] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 13:48:28] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 13:49:28] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 13:49:28] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 13:50:28] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 13:50:28] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 13:51:27] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 13:51:27] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 13:52:28] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 13:52:28] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 13:53:28] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 13:53:28] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 13:54:27] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 13:54:27] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 13:55:27] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 13:55:27] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 13:56:27] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 13:56:27] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 13:57:21] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 13:57:27] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 13:57:27] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 13:58:27] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 13:58:27] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 13:59:27] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 13:59:27] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 14:00:27] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 14:00:27] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 14:01:28] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 14:01:28] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 14:02:27] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
[2025-07-27 14:02:27] local.ERROR: require(D:\Workstation\Taswk\medhal\Packages\categories\src./../config/category.php): Failed to open stream: Permission denied {"exception":"[object] (ErrorException(code: 0): require(D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src./../config/category.php): Failed to open stream: Permission denied at D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php:138)
[stacktrace]
#0 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'require(D:\\\\Work...', 'D:\\\\Workstation\\\\...', 138)
#1 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(138): require()
#2 D:\\Workstation\\Taswk\\medhal\\Packages\\categories\\src\\CategoriesServiceProvider.php(35): Illuminate\\Support\\ServiceProvider->mergeConfigFrom('D:\\\\Workstation\\\\...', 'category')
#3 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(684): Tasawk\\Categories\\CategoriesServiceProvider->register()
#4 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\ProviderRepository.php(75): Illuminate\\Foundation\\Application->register(Object(Tasawk\\Categories\\CategoriesServiceProvider))
#5 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(661): Illuminate\\Foundation\\ProviderRepository->load(Array)
#6 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\RegisterProviders.php(17): Illuminate\\Foundation\\Application->registerConfiguredProviders()
#7 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(237): Illuminate\\Foundation\\Bootstrap\\RegisterProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#8 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(310): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#9 D:\\Workstation\\Taswk\\medhal\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(127): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#10 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 {main}
"} 
