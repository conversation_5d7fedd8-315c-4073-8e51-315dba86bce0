<link href="app-assets/css/icons/icomoon/styles.css" rel="stylesheet" type="text/css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/pnotify/3.0.0/pnotify.brighttheme.css"/>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/pnotify/3.0.0/pnotify.min.css"/>
<?php if(app()->getLocale() == "en"): ?>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,300;0,400;0,500;0,600;1,400;1,500;1,600"
          rel="stylesheet">
<?php endif; ?>
<?php if(app()->getLocale() == "ar"): ?>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;600;700;900&display=swap"
          rel="stylesheet">
<?php endif; ?>
<style>
    .file-icon p {
        font-size: 20px;
    }

    .navigation {
        font-size: inherit!important;
    }

    .modal .modal-header .close {
        <?php if(app()->getLocale()=="ar"): ?>
                       transform: translate(7px, 7px) !important;
        <?php else: ?>
                       transform: translate(-7px, 7px) !important;
        <?php endif; ?>
                     background: #f92929 !important;
        color: #fff !important;
    }
</style>
<?php if(\App::getLocale() == "en"): ?>
    <style>
        .main-menu.menu-dark .navigation > li ul li > a {
            padding: 10px 10px 10px 55px !important;
        }

        .dropify-wrapper .help-block {
            position: absolute;
            bottom: 0;
            left: 40%;
        }

        .dropify-wrapper .dropify-preview .dropify-infos .dropify-infos-inner p.dropify-infos-message {
            font-size: 25px !important;
        }

        .count_menu {
            float: right;
        }
    </style>
<?php else: ?>
    <style>
        body {
            font-family: 'Cairo', sans-serif !important;
        }

        #recipient-name {
            font-family: 'Cairo', sans-serif !important;
        }

        .navigation {
            font-family: 'Cairo', sans-serif !important;
        }

        .a_lang {
            font-family: 'Cairo', sans-serif !important;
        }

        .dropdown-user {
            font-family: 'Cairo', sans-serif !important;
        }

        .main-menu.menu-dark .navigation > li ul li > a {
            padding: 10px 50px 10px 15px !important;
        }

        .dropify-wrapper .help-block {
            position: absolute;
            bottom: 0;
            left: 40%;
        }

        .dropify-wrapper .dropify-preview .dropify-infos .dropify-infos-inner p.dropify-infos-message {
            font-size: 25px !important;
        }

        .count_menu {
            float: left;
        }

        .navigation-header span {
            font-family: 'Cairo', sans-serif !important;
        }

    </style>
<?php endif; ?>
<?php
$locale = (App::getLocale() == "ar") ? "-rtl" : "";
?>
<link href="<?php echo e(mix("/assets/tenant/site/css/tenant{$locale}.css")); ?>" rel="stylesheet" type="text/css">
<?php echo $__env->yieldPushContent('styles'); ?>
<script>
    var _token = '<?php echo e(csrf_token()); ?>';
    var base_url = "<?php echo e(url("/")); ?>/";

</script>
<?php echo $__env->yieldPushContent('scripts.head'); ?>
<?php /**PATH D:\Workstation\Taswk\medhal\Packages\admin-theme-vuexy\src/views/parts/head/assets.blade.php ENDPATH**/ ?>