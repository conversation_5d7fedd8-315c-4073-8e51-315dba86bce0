<!DOCTYPE html>
<html class="loading " lang="<?php echo e(app()->getLocale()); ?>" dir="<?php echo e(app()->getLocale() == 'ar' ? 'rtl' : 'ltr'); ?>"
    data-textdirection="<?php echo e(app()->getLocale() == 'ar' ? 'rtl' : 'ltr'); ?>">

<head>
    <?php if(app()->getLocale() == 'ar'): ?>
        <?php $__env->startPush('styles'); ?>
            <style>
                body {
                    background-color: aqua;
                }

                .breadcrumb:not([class*='breadcrumb-']) .breadcrumb-item+.breadcrumb-item:before {
                    content: ' ';
                    background-image: url(data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'none\' stroke=\'%236e6b7b\' stroke-width=\'2\' stroke-linecap=\'round\' stroke-linejoin=\'round\' class=\'feather feather-chevron-right\'%3E%3Cpolyline points=\'9 18 15 12 9 6\'%3E%3C/polyline%3E%3C/svg%3E);
                    background-repeat: no-repeat;
                    background-position: center;
                    color: #6E6B7B;
                    margin-left: 0.6rem;
                    background-size: 14px;
                    transform: rotate(180deg);
                }
            </style>
        <?php $__env->stopPush(); ?>
    <?php endif; ?>
    <?php echo $__env->make('admin::parts.head', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
</head>

<body data-menu="vertical-menu-modern"
    class="vertical-layout vertical-menu-modern  navbar-floating footer-static <?php if(setting_user()->get('menu-collapsed') == 'true'): ?> menu-collapsed <?php else: ?> menu-expanded <?php endif; ?>">

    <?php echo $__env->make('admin::parts.nav', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('admin::parts.main-menu', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <div class="app-content content  ecommerce-application ">
        <div class="content-overlay"></div>
        <div class="header-navbar-shadow"></div>
        <div class="content-wrapper container p-0">
            <?php echo $__env->make('admin::parts.breadcrumb', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <div class="content-body">
                <?php echo $__env->yieldContent('content'); ?>
            </div>
        </div>
    </div>
    <div class="sidenav-overlay"></div>
    <div class="drag-target"></div>
    <?php echo $__env->make('admin::parts.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php app('eventy')->action('admin.before.body'); ?>
</body>

</html>
<?php /**PATH D:\Workstation\Taswk\medhal\Packages\admin-theme-vuexy\src/views/layouts/2_col.blade.php ENDPATH**/ ?>