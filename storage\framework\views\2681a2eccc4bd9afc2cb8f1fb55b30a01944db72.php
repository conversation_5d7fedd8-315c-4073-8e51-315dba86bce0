<?php $__empty_1 = true; $__currentLoopData = $unreadNotificationsList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
    <?php
    $href = 'javascript:void(0)';
    $target = $notification->data['target'] ?? [];
    if (!empty($target)) {
        if (isset($target['type']) && $target['type'] == 'route') {
            $href = route($target['path'], array_values($target['params']) ?? []);
        }
        if (isset($target['type']) && $target['type'] == 'url') {
            $href = $target['path'];
        }
    }
    ?>
    <a class="d-flex notification-item" href="<?php echo e($href); ?>" data-notification-item-id="<?php echo e($notification->id); ?>">
        <div class="media d-flex align-items-start">
            <div class="media-body">
                <p class="media-heading">
                    <span class="font-weight-bolder"><?php echo e($notification->data['title'][app()->getLocale()] ?? $notification->data['title']); ?></span>
                </p>
                <small
                    class="notification-text"><?php echo e($notification->data['body'][app()->getLocale()] ?? ($notification->data['body'] ?? '')); ?><?php echo e($notification->data['description'][app()->getLocale()] ?? ($notification->data['description'] ?? '')); ?></small>
            </div>
        </div>
    </a>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
    <p class="text-center pt-1"><?php echo app('translator')->get('No new notifications'); ?></p>
<?php endif; ?>
<?php /**PATH D:\Workstation\Taswk\medhal\Packages\notifcation\app/../resources/views/themes/vuexy/parts/notifications-list.blade.php ENDPATH**/ ?>