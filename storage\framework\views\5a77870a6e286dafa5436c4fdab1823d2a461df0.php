<?php

use <PERSON><PERSON>\Menu\{Lara<PERSON>\Menu, Link};
use Tasawk\AppSettings\Setting\AppSettings;
use TorMorten\Eventy\Facades\Eventy;

if (!function_exists('settingsMenu')) {
    /**
     * @return array[]
     */
    function settingsMenu(): array
    {
        $pages = [];
        foreach (config('app_settings.pages', []) as $page => $section) {
            if (isset($section['hide_menu']) && $section['hide_menu']) {
                continue;
            }
            if ($page == 'my-profile') {
                continue;
            }
            $section['link'] = route('app_settings.index', ['page' => $page]);
            $section['permission'] = $section['permission'] ?? 'manage_settings';
            $pages[] = $section;
        }
        $settings = array_merge($pages, AppSettings::getStaticSections());
        $items[] = [
            'title' => __('General'),
            'path_type' => 'url',
            'path' => route('app_settings.index'),
            'permission' => 'manage_settings.general',
        ];
        $items[] = [
            'title' => __('My profile'),
            'path_type' => 'url',
            'path' => route('my_profile'),
            'permission' => 'manage_settings.profile',
        ];
//        $items[] = [
//            'title' => __('Report reasons'),
//            'path_type' => 'route',
//            'path' => 'admin.subscriptions.reports.reasons.index',
//            'permission' => 'reports.reasons.index',
//        ];
//        $items[] = [
//            'title' => __('Customers payments report'),
//            'path_type' => 'route',
//            'path' => 'admin.reports.customers.salesReport',
//            'permission' => 'reports.index',
//        ];
        $items[] = [
            'title' => __('Groups'),
            'path_type' => 'route',
            'path' => 'admin.roles.index',
            'permission' => 'roles.index',
        ];
        $items[] = [
            'title' => __('Admins'),
            'path_type' => 'route',
            'path' => 'admin.employee.index',
            'permission' => 'employees.index',
        ];
        $items[] = [
            'title' => __('Report reasons'),
            'path_type' => 'route',
            'path' => 'admin.subscriptions.reports.reasons.index',
            'permission' => "subscriptions-report.index"
        ];
        foreach ($settings as $section) {
            $items[] = [
                'title' => __($section['title']),
                'path_type' => 'url',
                'path' => $section['link'],
                'permission' => $section['permission'],
            ];
        }
        foreach (Config::get('app_settings.links') as $key => $link) {
            $items[$key] = [
                'title' => __($link['title']),
                'icon' => $link['icon'],
            ];
            if (Arr::has($link, 'items')) {
                foreach ($link['items'] as $item) {
                    $items[$key]['items'][] = [
                        'title' => __($item['title']),
                        'path_type' => 'url',
                        'path' => $item['url'],
                        'permission' => $item['permission'] ?? 'manage_settings',
                    ];
                }
            }
        }

        //        dd($items);
        return [
            'settings' => [
                'title' => __('Settings'),
                'icon' => 'icon-cog',
                'items' => array_values($items),
            ],
        ];
    }
}

$menusItems = [];
$menus = Eventy::filter('admin.sidebar.menu', $menusItems);
$groupsMenus = [
    'administration' => [
        'title' => __('Administration'),
    ],
    'site' => [
        'title' => __('Site'),
    ],
];

foreach ($groupsMenus as $id => $item) {
    $arr = Eventy::filter('admin.sidebar.menu.group.' . $id, []);
    usort($arr, function ($a, $b) {
        if (isset($a['order']) && isset($b['order'])) {
            return $b['order'] <=> $a['order'];
        }
        return 1;
    });
    $groupsMenus[$id]['menus'] = array_reverse($arr);
}
if (!function_exists('menu_item_badge')) {
    function menu_item_badge(int $count, $in_sub = false)
    {
        if ($count === 0) {
            return '';
        }
        $margin = $in_sub ? 'l' : 'r';
        return "<span class='badge badge-light-warning badge-pill ml-auto m{$margin}-1 count_menu'>" . $count . '</span>';
    }
}

if (!function_exists('build_menu')) {
    function build_menu(Menu $menu, array $menus)
    {
        foreach ($menus as $id => $menuItem) {
            if (is_string($id)) {
                $filteredMenuItem = Eventy::filter('admin.sidebar.menu.' . $id, $menuItem);
            } else {
                $filteredMenuItem = $menuItem;
            }
            $count = menu_item_badge($filteredMenuItem['count'] ?? 0);
            if (isset($filteredMenuItem['items'])) {
                $header = Link::to('/#', "<i class=\"{$filteredMenuItem['icon']}\"></i> <span>{$filteredMenuItem['title']}</span>{$count}");
                $submenu = Menu::new();
                foreach ($filteredMenuItem['items'] as $item) {
                    if (isset($item['items'])) {
                        $_submenu = Menu::new();
                        foreach ($item['items'] as $_item) {
                            $_item_count = menu_item_badge($_item['count'] ?? 0, true);
                            if (isset($_item['permission'])) {
                                $method = $_item['path_type'] . 'IfCan';
                                $_submenu->$method($_item['permission'], $_item['path'], "<span>{$_item['title']}</span> {$_item_count}");
                            }
                        }
                        $header_tag = html()
                            ->i()
                            ->attributes(['data-feather' => 'circle']);
                        $header_tag .= html()
                            ->span()
                            ->class('menu-item text-truncate')
                            ->text($item['title']);
                        $header_tag = Link::to('/#', $header_tag);
                        if ($_submenu->count()) {
                            $submenu->submenu($header_tag, $_submenu->addItemClass('d-flex align-items-center'));
                        }
                    } else {
                        $item_tag = html()
                            ->i()
                            ->attributes(['data-feather' => 'circle']);
                        $item_tag .= html()
                            ->span()
                            ->class('menu-item text-truncate')
                            ->text($item['title']);
                        if (isset($item['permission'])) {
                            $method = $item['path_type'] . 'IfCan';
                            $submenu->$method($item['permission'], $item['path'], $item_tag);
                        } else {
                            $submenu->{$item['path_type']}($item['path'], $item_tag);
                        }
                    }
                }

                $menu->submenu($header, $submenu);
            } else {
                if (isset($filteredMenuItem['permission'])) {
                    $method = $filteredMenuItem['path_type'] . 'IfCan';
                    $menu->$method($filteredMenuItem['permission'], $filteredMenuItem['path'], "<i class=\"{$filteredMenuItem['icon']}\"></i> <span>{$filteredMenuItem['title']}</span>{$count}");
                } else {
                    $menu->{$filteredMenuItem['path_type']}($filteredMenuItem['path'], "<i class=\"{$filteredMenuItem['icon']}\"></i> <span>{$filteredMenuItem['title']}</span>{$count}");
                }
            }
        }

        return $menu;
    }
}

?>
<?php
Eventy::action('limitless.sidebar.before');
$menu = Menu::new()
    ->addClass('navigation navigation-main navigation-accordion')
    ->setActiveFromRequest('https://yummi.yassir-hub.test/en/admin/crm/customers/*');
Eventy::action('limitless.sidebar.after');

$menu1 = Menu::new()->addClass('navigation navigation-main navigation-accordion');
$menu1->route('admin.index', '<i class="icon-home2"></i><span>' . __('Dashboard') . '</span>')->setActiveFromRequest('https://yummi.yassir-hub.test/en/admin/crm/customers/*');
?>


<ul class="navigation navigation-main navigation-accordion">
    <?php echo e($menu1); ?>

    <?php $__currentLoopData = $groupsMenus; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $id => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php if(empty($item['menus'])) continue; ?>
        <li class="navigation-header mt-0">
            <span class="navigation"><?php echo e($item['title']); ?></span>
            <i data-feather="more-horizontal"></i>
        </li>
        <?php $groupMenu = Menu::new()->addClass('navigation navigation-main navigation-accordion'); ?>
        <?php echo e(build_menu($groupMenu, $item['menus'])); ?>

    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    <li class="navigation-header mt-0">
        <span class="navigation "><?php echo app('translator')->get('General'); ?></span>
        <i data-feather="more-horizontal"></i>
    </li>
    <?php echo e(build_menu($menu, array_merge($menus, settingsMenu()))); ?>


</ul>
<?php /**PATH D:\Workstation\Taswk\medhal\Packages\admin-theme-vuexy\src/views/parts/menu-side-bar.blade.php ENDPATH**/ ?>