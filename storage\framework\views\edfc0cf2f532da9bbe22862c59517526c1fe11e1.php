
<?php $__env->startSection('head_title', __('Show subscription')); ?>
<?php $__env->startSection('title', __('Show subscription')); ?>
<?php $__env->startSection('content'); ?>
    <div class="row ">
        <div class="col-md-12">
            <?php echo $__env->make('subscriptions::components.basic-data', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php echo $__env->make('subscriptions::components.plan-data', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <?php echo $__env->make('subscriptions::components.reservation_data', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
        <div class="col-md-12">

            <?php echo $__env->renderWhen(
                $subscription->customerReport()->exists(),
                'subscriptions::components.report_subscription',
                [
                    'report' => $subscription->report,
                ]
            , \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path'])); ?>
            <?php echo $__env->make('subscriptions::components.subscription-rate', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
    </div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin::layouts.2_col', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Workstation\Taswk\medhal\Packages\subscriptions\src/../resources/views/themes/vuexy/Dashboard/show.blade.php ENDPATH**/ ?>