<div class="main-menu menu-fixed menu-light menu-accordion menu-shadow sidebar-main-menu" data-scroll-to-active="true">
    <div class="navbar-header sidebar-navbar-header">
        <ul class="nav navbar-nav flex-row">
            <li class="nav-item mr-auto">
                <a class="navbar-brand" href="<?php echo e(route('admin.index')); ?>">
                    <span class="brand-logo">
                  <img
                      src="<?php echo e(display_logo_avatar()); ?>"
                      alt="<?php echo e(settings()->group('default')->get('company_name',config('app.name'))); ?>"
                      style=" max-width: 25px; ">
                    </span>
                    <?php $brandText = settings()->group('default')->get('company_name', config('app.name'))?>
                    <h2 class="brand-text" title="<?php echo e($brandText); ?>"><?php echo e($brandText); ?></h2>
                </a>
            </li>
            <li class="nav-item nav-toggle">
                <a class="nav-link modern-nav-toggle pr-0" data-toggle="collapse"
                   data-user-setting='{"menu-collapsed":"<?php echo e(setting_user()->get('menu-collapsed') == "true" ? 'false' : 'true'); ?>"}'>
                    <i class="icomoon icon-radio-checked collapse-toggle-icon"></i>
                </a>
            </li>
        </ul>
    </div>
    <div class="shadow-bottom"></div>
    <div class="main-menu-content">
        <?php echo $__env->make('admin::parts.menu-side-bar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    </div>
</div>
<?php /**PATH D:\Workstation\Taswk\medhal\Packages\admin-theme-vuexy\src/views/parts/main-menu.blade.php ENDPATH**/ ?>